{"version": 4, "terraform_version": "1.11.4", "serial": 151, "lineage": "2f6aeb24-3bb6-c387-3d86-c03561ecd6aa", "outputs": {"function_url": {"value": "https://europe-west1-external-data-source-437915.cloudfunctions.net/billomat-ingestion", "type": "string"}}, "resources": [{"mode": "data", "type": "archive_file", "name": "function_source", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": null, "id": "4f4a020a86ddca5d55247c152d14dcd015f339b8", "output_base64sha256": "6MzasvphXFIrNznA/kmVmYH0CBkXxWi4U8NVnQd6uMY=", "output_base64sha512": "hS7gjIgW/kiV4U8z22cm7TZKVWSDzHSqFMF6zP2rJzLiHo6qC51z/7G0rLvHfoxEw1mOwBG61ar798LgFTongg==", "output_file_mode": null, "output_md5": "4f073285a7775e2390e67eb1ed1cb741", "output_path": "function-source.zip", "output_sha": "4f4a020a86ddca5d55247c152d14dcd015f339b8", "output_sha256": "e8ccdab2fa615c522b3739c0fe49959981f4081917c568b853c3559d077ab8c6", "output_sha512": "852ee08c8816fe4895e14f33db6726ed364a556483cc74aa14c17accfdab2732e21e8eaa0b9d73ffb1b4acbbc77e8c44c3598ec011bad5aafbf7c2e0153a2782", "output_size": 88857, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "deploy_temp", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "google_storage_bucket", "name": "existing_data_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"autoclass": [], "cors": [], "custom_placement_config": [], "default_event_based_hold": false, "effective_labels": {}, "enable_object_retention": false, "encryption": [], "force_destroy": null, "hierarchical_namespace": [{"enabled": false}], "id": "raw-data-external-source-437915-1", "labels": {}, "lifecycle_rule": [], "location": "EUROPE-WEST1", "logging": [], "name": "raw-data-external-source-437915-1", "project": "external-data-source-437915", "project_number": ***********, "public_access_prevention": "inherited", "requester_pays": false, "retention_policy": [], "rpo": null, "self_link": "https://www.googleapis.com/storage/v1/b/raw-data-external-source-437915-1", "soft_delete_policy": [{"effective_time": "2025-04-17T10:08:46.581Z", "retention_duration_seconds": 604800}], "storage_class": "NEARLINE", "terraform_labels": {}, "uniform_bucket_level_access": false, "url": "gs://raw-data-external-source-437915-1", "versioning": [], "website": []}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "google_cloud_scheduler_job", "name": "billomat_month_end", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"app_engine_http_target": [], "attempt_deadline": "180s", "description": "Triggers Billomat ingestion function on the last day of every month to check invoice status changes", "http_target": [{"body": "eyJtb250aF9lbmRfY2hlY2siOnRydWV9", "headers": {"Content-Type": "application/json"}, "http_method": "POST", "oauth_token": [], "oidc_token": [{"audience": "https://europe-west1-external-data-source-437915.cloudfunctions.net/billomat-ingestion", "service_account_email": "<EMAIL>"}], "uri": "https://europe-west1-external-data-source-437915.cloudfunctions.net/billomat-ingestion"}], "id": "projects/external-data-source-437915/locations/europe-west1/jobs/billomat-month-end-check", "name": "billomat-month-end-check", "paused": false, "project": "external-data-source-437915", "pubsub_target": [], "region": "europe-west1", "retry_config": [], "schedule": "30 23 28-31 * *", "state": "ENABLED", "time_zone": "Europe/Berlin", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["data.archive_file.function_source", "google_cloudfunctions_function.billomat_ingestion", "google_service_account.function_sa", "google_service_account.scheduler_sa", "google_storage_bucket.function_bucket", "google_storage_bucket_object.archive", "null_resource.run_prepare_script"]}]}, {"mode": "managed", "type": "google_cloud_scheduler_job", "name": "billomat_tuesday", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"app_engine_http_target": [], "attempt_deadline": "180s", "description": "Triggers Billomat ingestion function every Tuesday", "http_target": [{"body": "", "headers": {}, "http_method": "POST", "oauth_token": [], "oidc_token": [{"audience": "https://europe-west1-external-data-source-437915.cloudfunctions.net/billomat-ingestion", "service_account_email": "<EMAIL>"}], "uri": "https://europe-west1-external-data-source-437915.cloudfunctions.net/billomat-ingestion"}], "id": "projects/external-data-source-437915/locations/europe-west1/jobs/billomat-ingestion-tuesday", "name": "billomat-ingestion-tuesday", "paused": false, "project": "external-data-source-437915", "pubsub_target": [], "region": "europe-west1", "retry_config": [], "schedule": "0 23 * * 2", "state": "ENABLED", "time_zone": "Europe/Berlin", "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************", "dependencies": ["data.archive_file.function_source", "google_cloudfunctions_function.billomat_ingestion", "google_service_account.function_sa", "google_service_account.scheduler_sa", "google_storage_bucket.function_bucket", "google_storage_bucket_object.archive", "null_resource.run_prepare_script"]}]}, {"mode": "managed", "type": "google_cloudfunctions_function", "name": "billomat_ingestion", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"available_memory_mb": 512, "build_environment_variables": null, "build_service_account": "projects/external-data-source-437915/serviceAccounts/<EMAIL>", "build_worker_pool": "", "description": "Billomat data ingestion function", "docker_registry": "ARTIFACT_REGISTRY", "docker_repository": "", "effective_labels": {"goog-terraform-provisioned": "true"}, "entry_point": "billomat_http_trigger", "environment_variables": {"BILLOMAT_API_KEY": "********************************", "GCP_PROJECT": "external-data-source-437915", "GCS_BUCKET_NAME": "raw-data-external-source-437915-1"}, "event_trigger": [], "https_trigger_security_level": "SECURE_OPTIONAL", "https_trigger_url": "https://europe-west1-external-data-source-437915.cloudfunctions.net/billomat-ingestion", "id": "projects/external-data-source-437915/locations/europe-west1/functions/billomat-ingestion", "ingress_settings": "ALLOW_ALL", "kms_key_name": "", "labels": {}, "max_instances": 0, "min_instances": 0, "name": "billomat-ingestion", "project": "external-data-source-437915", "region": "europe-west1", "runtime": "python311", "secret_environment_variables": [], "secret_volumes": [], "service_account_email": "<EMAIL>", "source_archive_bucket": "external-data-source-437915-function-source", "source_archive_object": "function-source-4f073285a7775e2390e67eb1ed1cb741.zip", "source_repository": [], "status": "ACTIVE", "terraform_labels": {"goog-terraform-provisioned": "true"}, "timeout": 540, "timeouts": null, "trigger_http": true, "version_id": "10", "vpc_connector": "", "vpc_connector_egress_settings": ""}, "sensitive_attributes": [[{"type": "get_attr", "value": "environment_variables"}, {"type": "index", "value": {"value": "BILLOMAT_API_KEY", "type": "string"}}]], "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.archive_file.function_source", "google_service_account.function_sa", "google_storage_bucket.function_bucket", "google_storage_bucket_object.archive", "null_resource.run_prepare_script"]}]}, {"mode": "managed", "type": "google_cloudfunctions_function_iam_member", "name": "public_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"cloud_function": "projects/external-data-source-437915/locations/europe-west1/functions/billomat-ingestion", "condition": [], "etag": "BwY0JSgKjRQ=", "id": "projects/external-data-source-437915/locations/europe-west1/functions/billomat-ingestion/roles/cloudfunctions.invoker/allUsers", "member": "allUsers", "project": "external-data-source-437915", "region": "europe-west1", "role": "roles/cloudfunctions.invoker"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.archive_file.function_source", "google_cloudfunctions_function.billomat_ingestion", "google_service_account.function_sa", "google_storage_bucket.function_bucket", "google_storage_bucket_object.archive", "null_resource.run_prepare_script"]}]}, {"mode": "managed", "type": "google_cloudfunctions_function_iam_member", "name": "scheduler_invoker", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"cloud_function": "projects/external-data-source-437915/locations/europe-west1/functions/billomat-ingestion", "condition": [], "etag": "BwY0JSgKjRQ=", "id": "projects/external-data-source-437915/locations/europe-west1/functions/billomat-ingestion/roles/cloudfunctions.invoker/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "external-data-source-437915", "region": "europe-west1", "role": "roles/cloudfunctions.invoker"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.archive_file.function_source", "google_cloudfunctions_function.billomat_ingestion", "google_service_account.function_sa", "google_service_account.scheduler_sa", "google_storage_bucket.function_bucket", "google_storage_bucket_object.archive", "null_resource.run_prepare_script"]}]}, {"mode": "managed", "type": "google_service_account", "name": "function_sa", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "billomat-function-sa", "create_ignore_already_exists": null, "description": "", "disabled": false, "display_name": "Service Account for Billomat Cloud Function", "email": "<EMAIL>", "id": "projects/external-data-source-437915/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/external-data-source-437915/serviceAccounts/<EMAIL>", "project": "external-data-source-437915", "timeouts": null, "unique_id": "116474836560525120801"}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "google_service_account", "name": "scheduler_sa", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "billomat-scheduler", "create_ignore_already_exists": null, "description": "", "disabled": false, "display_name": "Service Account for <PERSON><PERSON><PERSON> Scheduler", "email": "<EMAIL>", "id": "projects/external-data-source-437915/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/external-data-source-437915/serviceAccounts/<EMAIL>", "project": "external-data-source-437915", "timeouts": null, "unique_id": "100235278461339140839"}, "sensitive_attributes": [], "private": "****************************************************************************************"}]}, {"mode": "managed", "type": "google_storage_bucket", "name": "function_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 3, "attributes": {"autoclass": [], "cors": [], "custom_placement_config": [], "default_event_based_hold": false, "effective_labels": {"goog-terraform-provisioned": "true"}, "enable_object_retention": false, "encryption": [], "force_destroy": true, "hierarchical_namespace": [{"enabled": false}], "id": "external-data-source-437915-function-source", "labels": {}, "lifecycle_rule": [], "location": "EUROPE-WEST1", "logging": [], "name": "external-data-source-437915-function-source", "project": "external-data-source-437915", "project_number": ***********, "public_access_prevention": "inherited", "requester_pays": false, "retention_policy": [], "rpo": null, "self_link": "https://www.googleapis.com/storage/v1/b/external-data-source-437915-function-source", "soft_delete_policy": [{"effective_time": "2025-05-02T11:05:11.952Z", "retention_duration_seconds": 604800}], "storage_class": "STANDARD", "terraform_labels": {"goog-terraform-provisioned": "true"}, "timeouts": null, "uniform_bucket_level_access": true, "url": "gs://external-data-source-437915-function-source", "versioning": [], "website": []}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "google_storage_bucket_iam_member", "name": "function_storage_admin", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "b/raw-data-external-source-437915-1", "condition": [], "etag": "CAw=", "id": "b/raw-data-external-source-437915-1/roles/storage.admin/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "role": "roles/storage.admin"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.google_storage_bucket.existing_data_bucket", "google_service_account.function_sa"]}]}, {"mode": "managed", "type": "google_storage_bucket_object", "name": "archive", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "external-data-source-437915-function-source", "cache_control": "", "content": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/zip", "crc32c": "PcVPdg==", "customer_encryption": [], "detect_md5hash": "Twcyhad3XiOQ5n6x7Ry3QQ==", "event_based_hold": false, "generation": ****************, "id": "external-data-source-437915-function-source-function-source-4f073285a7775e2390e67eb1ed1cb741.zip", "kms_key_name": "", "md5hash": "Twcyhad3XiOQ5n6x7Ry3QQ==", "md5hexhash": "4f073285a7775e2390e67eb1ed1cb741", "media_link": "https://storage.googleapis.com/download/storage/v1/b/external-data-source-437915-function-source/o/function-source-4f073285a7775e2390e67eb1ed1cb741.zip?generation=****************&alt=media", "metadata": null, "name": "function-source-4f073285a7775e2390e67eb1ed1cb741.zip", "output_name": "function-source-4f073285a7775e2390e67eb1ed1cb741.zip", "retention": [], "self_link": "https://www.googleapis.com/storage/v1/b/external-data-source-437915-function-source/o/function-source-4f073285a7775e2390e67eb1ed1cb741.zip", "source": "function-source.zip", "storage_class": "STANDARD", "temporary_hold": false, "timeouts": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "content"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoyNDAwMDAwMDAwMDAsImRlbGV0ZSI6MjQwMDAwMDAwMDAwLCJ1cGRhdGUiOjI0MDAwMDAwMDAwMH19", "dependencies": ["data.archive_file.function_source", "google_storage_bucket.function_bucket", "null_resource.run_prepare_script"]}]}, {"mode": "managed", "type": "null_resource", "name": "cleanup_deployment", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"schema_version": 0, "attributes": {"id": "7068082292264227598", "triggers": {"always_run": "2025-05-09T10:00:04Z"}}, "sensitive_attributes": [], "dependencies": ["data.archive_file.function_source", "google_storage_bucket.function_bucket", "google_storage_bucket_object.archive", "null_resource.run_prepare_script"]}]}, {"mode": "managed", "type": "null_resource", "name": "run_prepare_script", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"schema_version": 0, "attributes": {"id": "5269739664907843362", "triggers": {"source_hash": "fc8f19faf734d58286ce65860cac92f565b5937591f9fbafd04d99851bc73507"}}, "sensitive_attributes": []}]}], "check_results": null}