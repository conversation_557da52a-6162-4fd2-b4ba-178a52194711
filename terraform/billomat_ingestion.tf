

# # Terraform configuration for Billomat ingestion Cloud Function
# provider "google" {
#   project = var.project_id
#   region  = var.region
# }

# # Variables
# variable "project_id" {
#   description = "Google Cloud Project ID"
#   type        = string
# }

# variable "region" {
#   description = "Google Cloud Region"
#   type        = string
#   default     = "europe-west1"  # google cloud locations as per the google cloud functions locations availaibility
# }

# variable "bucket_name" {
#   description = "GCS Bucket Name"
#   type        = string
# }

# variable "billomat_api_key" {
#   description = "Billomat API Key"
#   type        = string
#   sensitive   = true
# }

# # Cloud Storage Bucket for function code
# resource "google_storage_bucket" "function_bucket" {
#   name     = "${var.project_id}-function-source"
#   location = var.region
#   uniform_bucket_level_access = true
#   force_destroy = true # only set for the force destroy method true
# }

# data "google_storage_bucket" "existing_data_bucket" {
#   name = var.bucket_name
# }

# # Data bucket (if it doesn't already exist)
# # resource "google_storage_bucket" "data_bucket" {
# #   count    = var.bucket_name == "${var.project_id}-function-source" ? 0 : 1
# #   name     = var.bucket_name
# #   location = var.region
# #   uniform_bucket_level_access = true
# #   force_destroy = false
# # }
# resource "google_storage_bucket_iam_member" "function_storage_admin" {
#   bucket = data.google_storage_bucket.existing_data_bucket.name
#   role   = "roles/storage.admin"
#   member = "serviceAccount:${google_service_account.function_sa.email}"
# }

# # Service account for the Cloud Function
# resource "google_service_account" "function_sa" {
#   account_id   = "billomat-function-sa"
#   display_name = "Service Account for Billomat Cloud Function"
# }


# # ZIP archive for Cloud Function source code
# data "archive_file" "function_source" {
#   type        = "zip"
#   # source_dir  = "../src/cloud_functions/ingestions/billomat"
#   # source_dir = "../src"
#   source_dir = "deploy_temp"
#   output_path = "function-source.zip"
#   depends_on  = [null_resource.prepare_deployment]
# }

# resource "null_resource" "prepare_deployment" {
#   triggers = {
#     # Re-run on any change to the source files
#     source_hash = filesha256("${path.module}/../src/cloud_functions/ingestions/billomat/main.py")
#   }

#   provisioner "local-exec" {
#     command = <<-EOT
#       # Create deployment directory
#       mkdir -p deploy_temp

#       # Copy the cloud function files to the root of the deployment directory
#       cp -r ${path.module}/../src/cloud_functions/ingestions/billomat/* deploy_temp/

#       # Create a src directory and copy all source files there
#       mkdir -p deploy_temp/src
#       cp -r ${path.module}/../src/* deploy_temp/src/

#       # Create necessary __init__.py files
#       touch deploy_temp/src/__init__.py
#       find deploy_temp/src -type d -exec touch {}/__init__.py \; 2>/dev/null || true

#       echo "Deployment directory prepared successfully!"
#     EOT
#   }
# }

# # Upload ZIP to bucket
# resource "google_storage_bucket_object" "archive" {
#   name   = "function-source-${data.archive_file.function_source.output_md5}.zip"
#   bucket = google_storage_bucket.function_bucket.name
#   source = data.archive_file.function_source.output_path
# }

# # Cloud Function
# resource "google_cloudfunctions_function" "billomat_ingestion" {
#   name        = "billomat-ingestion"
#   description = "Billomat data ingestion function"
#   runtime     = "python311"  # python version function

#   available_memory_mb   = 512  # Increased memory
#   source_archive_bucket = google_storage_bucket.function_bucket.name
#   source_archive_object = google_storage_bucket_object.archive.name
#   trigger_http          = true
#   timeout               = 540  # 9 minutes for running the standard functions
#   entry_point           = "billomat_http_trigger"  # Changed to HTTP trigger function
#   service_account_email = google_service_account.function_sa.email

#   environment_variables = {
#     GCP_PROJECT = var.project_id
#     GCS_BUCKET_NAME = "raw-data-external-source-437915-1"
#     BILLOMAT_API_KEY = var.billomat_api_key
#   }
# }

# # IAM entry for allowing public (unauthenticated) access for testing
# resource "google_cloudfunctions_function_iam_member" "public_invoker" {
#   project        = google_cloudfunctions_function.billomat_ingestion.project
#   region         = google_cloudfunctions_function.billomat_ingestion.region
#   cloud_function = google_cloudfunctions_function.billomat_ingestion.name

#   role   = "roles/cloudfunctions.invoker"
#   member = "allUsers"
# }

# # Service account for Cloud Scheduler
# resource "google_service_account" "scheduler_sa" {
#   account_id   = "billomat-scheduler"
#   display_name = "Service Account for Billomat Scheduler"
# }

# # Grant the scheduler service account permission to invoke the function
# resource "google_cloudfunctions_function_iam_member" "scheduler_invoker" {
#   project        = google_cloudfunctions_function.billomat_ingestion.project
#   region         = google_cloudfunctions_function.billomat_ingestion.region
#   cloud_function = google_cloudfunctions_function.billomat_ingestion.name

#   role   = "roles/cloudfunctions.invoker"
#   member = "serviceAccount:${google_service_account.scheduler_sa.email}"
# }

# # Cloud Scheduler - Tuesday job
# resource "google_cloud_scheduler_job" "billomat_tuesday" {
#   name        = "billomat-ingestion-tuesday"
#   description = "Triggers Billomat ingestion function every Tuesday"
#   schedule    = "0 23 * * 2"  # Run the job on tuesday at 11PM i.e 24 april 2024
#   time_zone   = "Europe/Berlin"  # Adjust as needed

#   http_target {
#     uri         = google_cloudfunctions_function.billomat_ingestion.https_trigger_url
#     http_method = "POST"

#     oidc_token {
#       service_account_email = google_service_account.scheduler_sa.email
#     }
#   }
# }

# # # Cloud Scheduler - Friday job
# # resource "google_cloud_scheduler_job" "billomat_friday" {
# #   name        = "billomat-ingestion-friday"
# #   description = "Triggers Billomat ingestion function every Friday"
# #   schedule    = "0 5 * * 5"  # Run at 5:00 AM every Friday (day 5)
# #   time_zone   = "Europe/Berlin"  # Adjust as needed

# #   http_target {
# #     uri         = google_cloudfunctions_function.billomat_ingestion.https_trigger_url
# #     http_method = "POST"

# #     oidc_token {
# #       service_account_email = google_service_account.scheduler_sa.email
# #     }
# #   }
# # }

# # Cloud Scheduler - Month-End job for invoices check(runs on the last day of every month)
# resource "google_cloud_scheduler_job" "billomat_month_end" {
#   name        = "billomat-month-end-check"
#   description = "Triggers Billomat ingestion function on the last day of every month to check invoice status changes"
#   # Run at 23:00 on the last day of the month
#   schedule    = "30 23 28-31 * *"
#   time_zone   = "Europe/Berlin"

#   http_target {
#     uri         = google_cloudfunctions_function.billomat_ingestion.https_trigger_url
#     http_method = "POST"

#     # Adding a custom header to identify this as a month-end run (optional)
#     headers = {
#       "Content-Type" = "application/json"
#     }

#     # Send a special payload to indicate this is a month-end check
#     body = base64encode(jsonencode({
#       "month_end_check": true
#     }))

#     oidc_token {
#       service_account_email = google_service_account.scheduler_sa.email
#     }
#   }
# }



# resource "null_resource" "cleanup_deployment" {
#   depends_on = [google_storage_bucket_object.archive]

#   triggers = {
#     # Always run
#     always_run = timestamp()
#   }

#   provisioner "local-exec" {
#     command = "rm -rf deploy_temp"
#   }
# }


# # Output the function URL for easy testing
# output "function_url" {
#   value = google_cloudfunctions_function.billomat_ingestion.https_trigger_url
#   description = "The URL to trigger the Billomat ingestion function"
# }


# Terraform configuration for Billomat ingestion Cloud Function
# Provider and variables are defined in provider.tf

# Use data source for existing function bucket
data "google_storage_bucket" "function_bucket" {
  name = "${var.project_id}-function-source"
}

data "google_storage_bucket" "existing_data_bucket" {
  name = var.bucket_name
}

resource "google_storage_bucket_iam_member" "function_storage_admin" {
  bucket = data.google_storage_bucket.existing_data_bucket.name
  role   = "roles/storage.admin"
  member = "serviceAccount:${var.project_id}@appspot.gserviceaccount.com"
}

# Using default App Engine service account instead of custom service accounts
# to avoid permission issues with service account creation

# resource "google_service_account" "function_sa" {
#   account_id   = "billomat-function-sa"
#   display_name = "Service Account for Billomat Cloud Function"
#
#   lifecycle {
#     ignore_changes = [
#       # Ignore changes if service account already exists
#       display_name
#     ]
#   }
# }

# Run the preparation script before any Terraform operations
resource "null_resource" "run_prepare_script" {
  triggers = {
    # Re-run on any change to the source files
    source_hash = filesha256("${path.module}/../src/cloud_functions/ingestions/billomat/main.py")
  }

  provisioner "local-exec" {
    command = "bash ${path.module}/prepare_deployment.sh"
  }
}

# ZIP archive for Cloud Function source code
data "archive_file" "function_source" {
  type        = "zip"
  source_dir  = "deploy_temp"
  output_path = "function-source.zip"
  depends_on  = [null_resource.run_prepare_script]
}

# Upload ZIP to bucket
resource "google_storage_bucket_object" "archive" {
  name   = "function-source-${data.archive_file.function_source.output_md5}.zip"
  bucket = data.google_storage_bucket.function_bucket.name
  source = data.archive_file.function_source.output_path
}

# Cloud Function
resource "google_cloudfunctions_function" "billomat_ingestion" {
  name        = "complete-data-ingestion"
  description = "Complete Data Ingestion function for Billomat"
  runtime     = "python311"  # python version function

  available_memory_mb   = 512  # Increased memory
  source_archive_bucket = data.google_storage_bucket.function_bucket.name
  source_archive_object = google_storage_bucket_object.archive.name
  trigger_http          = true
  timeout               = 540  # 9 minutes for running the standard functions
  entry_point           = "billomat_http_trigger"  # Changed to HTTP trigger function
  # Use default App Engine service account to avoid permission issues
  service_account_email = "${var.project_id}@appspot.gserviceaccount.com"

  environment_variables = {
    GCP_PROJECT = var.project_id
    GCS_BUCKET_NAME = "raw-data-external-source-437915-1"
    BILLOMAT_API_KEY = var.billomat_api_key
  }
}

# IAM entry for allowing public (unauthenticated) access for testing
resource "google_cloudfunctions_function_iam_member" "public_invoker" {
  project        = google_cloudfunctions_function.billomat_ingestion.project
  region         = google_cloudfunctions_function.billomat_ingestion.region
  cloud_function = google_cloudfunctions_function.billomat_ingestion.name

  role   = "roles/cloudfunctions.invoker"
  member = "allUsers"
}

# Using default App Engine service account for scheduler as well
# resource "google_service_account" "scheduler_sa" {
#   account_id   = "billomat-scheduler"
#   display_name = "Service Account for Billomat Scheduler"
#
#   lifecycle {
#     ignore_changes = [
#       # Ignore changes if service account already exists
#       display_name
#     ]
#   }
# }

# Grant the default service account permission to invoke the function
resource "google_cloudfunctions_function_iam_member" "scheduler_invoker" {
  project        = google_cloudfunctions_function.billomat_ingestion.project
  region         = google_cloudfunctions_function.billomat_ingestion.region
  cloud_function = google_cloudfunctions_function.billomat_ingestion.name

  role   = "roles/cloudfunctions.invoker"
  member = "serviceAccount:${var.project_id}@appspot.gserviceaccount.com"
}

# Cloud Scheduler - Tuesday job
resource "google_cloud_scheduler_job" "billomat_tuesday" {
  name        = "complete-data-ingestion-tuesday"
  description = "Triggers Complete Data Ingestion function every Tuesday"
  schedule    = "0 23 * * 2"  # Run the job on tuesday at 11PM i.e 24 april 2024
  time_zone   = "Europe/Berlin"  # Adjust as needed

  http_target {
    uri         = google_cloudfunctions_function.billomat_ingestion.https_trigger_url
    http_method = "POST"

    oidc_token {
      service_account_email = "${var.project_id}@appspot.gserviceaccount.com"
    }
  }
}

# Cloud Scheduler - Month-End job for invoices check (runs on the last day of every month)
resource "google_cloud_scheduler_job" "billomat_month_end" {
  name        = "complete-data-ingestion-month-end"
  description = "Triggers Complete Data Ingestion function on the last day of every month to check invoice status changes"
  # Run at 23:30 on the last day of the month
  schedule    = "30 23 28-31 * *"
  time_zone   = "Europe/Berlin"

  http_target {
    uri         = google_cloudfunctions_function.billomat_ingestion.https_trigger_url
    http_method = "POST"

    # Adding a custom header to identify this as a month-end run (optional)
    headers = {
      "Content-Type" = "application/json"
    }

    # Send a special payload to indicate this is a month-end check
    body = base64encode(jsonencode({
      "month_end_check": true
    }))

    oidc_token {
      service_account_email = "${var.project_id}@appspot.gserviceaccount.com"
    }
  }
}

resource "null_resource" "cleanup_deployment" {
  depends_on = [
    google_storage_bucket_object.archive,
    google_cloudfunctions_function.billomat_ingestion
  ]

  triggers = {
    # Always run
    always_run = timestamp()
  }

  provisioner "local-exec" {
    command = "rm -rf deploy_temp"
  }
}

# Output the function URL for easy testing
output "function_url" {
  value = google_cloudfunctions_function.billomat_ingestion.https_trigger_url
  description = "The URL to trigger the Complete Data Ingestion function"
}