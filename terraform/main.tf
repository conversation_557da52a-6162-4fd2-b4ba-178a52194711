# This file is disabled to avoid conflicts with billomat_ingestion.tf
# All resources are now defined in billomat_ingestion.tf

# locals {
#   function_name = "billomat-data-ingestion"
#   bucket_name   = "billomat-data-bucket"
# }

# resource "google_storage_bucket" "data_bucket" {
#   name     = local.bucket_name
#   location = var.region
#   force_destroy = true
# }

# data "archive_file" "function_zip" {
#   type        = "zip"
#   source_dir  = "${path.module}/deploy_temp"
#   output_path = "${path.module}/function-source.zip"
# }

# resource "google_storage_bucket_object" "function_zip" {
#   name   = "function-source-${data.archive_file.function_zip.output_md5}.zip"
#   bucket = google_storage_bucket.data_bucket.name
#   source = data.archive_file.function_zip.output_path
# }

# resource "google_cloudfunctions_function" "billomat_function" {
#   name        = local.function_name
#   description = "Billomat data ingestion function"
#   runtime     = "python311"

#   available_memory_mb   = 256
#   source_archive_bucket = google_storage_bucket.data_bucket.name
#   source_archive_object = google_storage_bucket_object.function_zip.name
#   trigger_http          = true
#   entry_point           = "process_billomat_data"

#   environment_variables = {
#     BILLOMAT_API_KEY = var.billomat_api_key
#     GCS_BUCKET_NAME  = google_storage_bucket.data_bucket.name
#   }
# }

# # IAM entry for all users to invoke the function
# resource "google_cloudfunctions_function_iam_member" "invoker" {
#   project        = var.project_id
#   region         = var.region
#   cloud_function = google_cloudfunctions_function.billomat_function.name

#   role   = "roles/cloudfunctions.invoker"
#   member = "allUsers"
# }

# # Cloud Scheduler job to trigger the function
# resource "google_cloud_scheduler_job" "billomat_job" {
#   name             = "billomat-data-ingestion-job"
#   description      = "Triggers the Billomat data ingestion function"
#   schedule         = "0 */6 * * *"  # Every 6 hours
#   time_zone        = "Europe/Berlin"
#   attempt_deadline = "320s"

#   http_target {
#     http_method = "GET"
#     uri         = google_cloudfunctions_function.billomat_function.https_trigger_url

#     oidc_token {
#       service_account_email = "${var.project_id}@appspot.gserviceaccount.com"
#     }
#   }
# }
