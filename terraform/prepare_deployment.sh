#!/bin/bash
# Script to prepare the deployment directory for Terraform

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Path to the source code
SRC_PATH="${SCRIPT_DIR}/../src"
BILLOMAT_PATH="${SRC_PATH}/cloud_functions/ingestions/billomat"

echo "Creating deploy_temp directory..."
rm -rf deploy_temp
mkdir -p deploy_temp

echo "Copying cloud function files..."
cp -r "${BILLOMAT_PATH}"/* deploy_temp/

echo "Creating src directory and copying source files..."
mkdir -p deploy_temp/src
cp -r "${SRC_PATH}"/* deploy_temp/src/

echo "Creating __init__.py files..."
touch deploy_temp/src/__init__.py
find deploy_temp/src -type d -exec touch {}/__init__.py \; 2>/dev/null || true

echo "Deployment directory prepared successfully!"

# Print content of the deploy_temp directory for verification
echo "Contents of deploy_temp directory:"
ls -la deploy_temp
echo "Contents of deploy_temp/src directory:"
ls -la deploy_temp/src