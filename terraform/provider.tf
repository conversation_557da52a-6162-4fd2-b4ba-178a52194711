provider "google" {
  project = var.project_id
  region  = var.region
}

variable "project_id" {
  description = "The ID of the Google Cloud project"
  type        = string
}

variable "region" {
  description = "The region to deploy resources to"
  type        = string
}

variable "bucket_name" {
  description = "GCS Bucket Name"
  type        = string
}

variable "billomat_api_key" {
  description = "The Billomat API key"
  type        = string
  sensitive   = true
}
