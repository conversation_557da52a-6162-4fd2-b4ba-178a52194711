#!/bin/bash

# Run tests for the Billomat Data Integration project
# This script runs all tests and generates a coverage report

# Set up environment variables for testing
export PYTHONPATH=$(pwd)
export BILLOMAT_API_KEY="test_api_key"
export GCS_BUCKET_NAME="test-bucket"

# Set timeout for tests to prevent hanging
export TEST_TIMEOUT=300  # 5 minutes timeout

# Check if required packages are installed
echo "Checking required packages..."
pip install -q coverage pytest pytest-timeout

# Run end-to-end tests with timeout
echo "Running end-to-end tests with timeout..."
# python -m pytest tests/e2e/test_billomat_e2e.py -v --timeout=$TEST_TIMEOUT

python -m pytest tests/e2e/test_billomat_e2e.py -v --html=test-report.html --self-contained-html

# Run all tests with coverage
echo "Running all tests with coverage..."
coverage run -m pytest tests

# Generate coverage report
echo "Generating coverage report..."
coverage report -m
coverage html

echo "Tests completed. See htmlcov/index.html for detailed coverage report."

# Optional: Run specific test methods if needed
# Uncomment the line below to run a specific test method
# python -m pytest tests/e2e/test_billomat_e2e.py::TestBillomatE2E::test_client_get_invoice -v

echo "All tests completed."
