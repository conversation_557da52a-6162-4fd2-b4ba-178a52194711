# GitLab CI/CD Pipeline Troubleshooting Guide

This guide provides solutions to common issues encountered with the GitLab CI/CD pipeline for the Billomat Data Integration project.

## Common Issues and Solutions

### 1. "Externally-managed-environment" Error with pip

**Problem**: The pipeline fails with the error "error: externally-managed-environment × This environment is externally managed" when trying to install Python packages with pip.

**Solution**: This error occurs because Alpine Linux 3.21 uses Python 3.12, which enforces PEP 668 that prevents installing packages directly into the system Python environment.

**Fix**:
- Added py3-virtualenv package to the Alpine installation
- Created a Python virtual environment using `python3 -m venv /tmp/venv`
- Activated the virtual environment before installing packages
- Installed required packages within the virtual environment

### 2. "Terraform has no command named 'sh'" Error

**Problem**: The pipeline fails with the error "Terraform has no command named 'sh'. Did you mean 'show'?"

**Solution**: This error occurs because the `hashicorp/terraform:latest` Docker image doesn't include bash or other shell utilities. The pipeline has been updated to use `alpine:latest` instead, with Terraform installed manually.

**Fix**:
- The `deploy_to_gcp` and `terraform_plan` jobs now use `alpine:latest` as the base image
- Terraform is installed in the `before_script` section
- Shell scripts are executed using bash

### 3. "Cannot delete old terraform: Is a directory" Error

**Problem**: The pipeline fails with the error "error: cannot delete old terraform. Is a directory" when trying to unzip the Terraform binary.

**Solution**: This error occurs because there's already a directory named "terraform" in the working directory, and the unzip command is trying to extract the Terraform binary to a file with the same name.

**Fix**:
- Created a temporary directory for extracting Terraform
- Changed to the temporary directory before unzipping
- Moved the Terraform binary to /usr/local/bin
- Changed back to the original directory after installation

### 4. Cleanup Running Before Deployment Completes or When Deployment Wasn't Run

**Problem**: The cleanup stage runs before the deployment stage completes or runs even when the deployment stage wasn't executed, potentially causing issues with the deployment process.

**Solution**: The cleanup job has been updated to only run when the deploy_to_gcp job has been executed and completed.

**Fix**:
- Added a marker file (/tmp/deployment_executed) that gets created when the deployment job runs
- Modified the cleanup job to check for the existence of this file before performing cleanup
- Added `dependencies: - deploy_to_gcp` to the cleanup job
- Added `needs: - job: deploy_to_gcp` with `optional: true` to ensure proper job ordering
- Added `allow_failure: true` to prevent the pipeline from failing if cleanup encounters issues

### 5. Authentication Issues

**Problem**: The pipeline fails with authentication errors when trying to access Google Cloud Platform.

**Solution**: Ensure that the GCP service account key is correctly set in the GitLab CI/CD variables.

**Fix**:
- Check that the `GCP_SERVICE_ACCOUNT_KEY` variable is set in GitLab CI/CD settings
- Verify that the service account has the necessary permissions
- The pipeline now uses a more robust authentication process

### 6. Deployment Script Execution Issues

**Problem**: The deployment script fails to execute or encounters errors during execution.

**Solution**: A dedicated deployment script (`ci/deploy_to_gcp.sh`) has been created to handle the deployment process.

**Fix**:
- The script includes proper error handling
- It checks for required environment variables
- It executes the deployment steps in the correct order

## How to Debug Pipeline Issues

### 1. Check the Pipeline Logs

The most important step in debugging pipeline issues is to check the logs:

1. Go to CI/CD > Pipelines
2. Click on the failed pipeline
3. Click on the failed job
4. Review the logs for error messages

### 2. Test Locally

You can test the deployment process locally to identify issues:

```bash
# Set the required environment variables
export GCP_PROJECT_ID=your_project_id
export GCP_REGION=your_region
export BILLOMAT_API_KEY=your_api_key
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# Run the deployment script
cd ci
bash deploy_to_gcp.sh
```

### 3. Validate Terraform Configuration

Validate your Terraform configuration locally:

```bash
cd terraform
terraform init
terraform validate
```

### 4. Check Service Account Permissions

Ensure your service account has the necessary permissions:

- Cloud Functions Admin
- Cloud Scheduler Admin
- Storage Admin
- Service Account User

## Advanced Troubleshooting

### 1. Manual Deployment

If the pipeline continues to fail, you can deploy manually:

```bash
cd terraform
terraform init
terraform plan -var="project_id=your_project_id" -var="region=your_region" -var="billomat_api_key=your_api_key"
bash prepare_deployment.sh
terraform apply -var="project_id=your_project_id" -var="region=your_region" -var="billomat_api_key=your_api_key"
```

### 2. Debugging the CI/CD Environment

To debug the CI/CD environment, add the following to your job script:

```yaml
script:
  - echo "Debugging CI/CD environment..."
  - env | sort
  - ls -la
  - pwd
  # Your regular script commands
```

### 3. Testing Individual Components

Test individual components of the pipeline:

```bash
# Test Terraform installation
terraform version

# Test GCP authentication
gcloud auth list

# Test deployment package creation
bash terraform/prepare_deployment.sh
```

## Contact Support

If you continue to experience issues with the CI/CD pipeline, please contact the project maintainer or open an issue in the GitLab repository.
