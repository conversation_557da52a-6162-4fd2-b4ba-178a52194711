# Setting Up GitLab CI/CD for Billomat Data Integration

This guide provides detailed instructions for setting up the GitLab CI/CD pipeline for the Billomat Data Integration project.

## Prerequisites

Before setting up the CI/CD pipeline, ensure you have:

1. A GitLab account with access to create repositories
2. A Google Cloud Platform (GCP) account with billing enabled
3. Appropriate permissions to create service accounts in GCP
4. Billomat API credentials

## Step 1: Create a GitLab Repository

1. Log in to your GitLab account
2. Click on "New project" > "Create blank project"
3. Enter a project name (e.g., "billomat-data-integration")
4. Set the visibility level (Private recommended for production)
5. Click "Create project"

## Step 2: Push Your Code to GitLab

1. Initialize a Git repository in your local project directory (if not already done):
   ```bash
   cd google_cloud_storage
   git init
   ```

2. Add the GitLab repository as a remote:
   ```bash
   git remote add origin https://gitlab.com/your-username/billomat-data-integration.git
   ```

3. Add, commit, and push your code:
   ```bash
   git add .
   git commit -m "Initial commit"
   git push -u origin main
   ```

## Step 3: Create a GCP Service Account for CI/CD

1. Go to the Google Cloud Console: https://console.cloud.google.com/
2. Navigate to "IAM & Admin" > "Service Accounts"
3. Click "Create Service Account"
4. Enter a name (e.g., "gitlab-ci-cd")
5. Add the following roles:
   - Cloud Functions Admin
   - Cloud Scheduler Admin
   - Storage Admin
   - Service Account User
   - Cloud Run Admin
   - Logs Viewer
6. Click "Create"
7. Click on the newly created service account
8. Go to the "Keys" tab
9. Click "Add Key" > "Create new key"
10. Select "JSON" and click "Create"
11. Save the downloaded JSON key file securely

## Step 4: Configure GitLab CI/CD Variables

1. In your GitLab project, go to "Settings" > "CI/CD"
2. Expand the "Variables" section
3. Add the following variables:
   - `GCP_SERVICE_ACCOUNT_KEY`: Copy and paste the entire content of the JSON key file
   - `GCP_PROJECT_ID`: Your Google Cloud project ID
   - `GCP_REGION`: Your Google Cloud region (e.g., "europe-west1")
   - `GCS_BUCKET_NAME`: Your Google Cloud Storage bucket name
   - `BILLOMAT_API_KEY`: Your Billomat API key
4. Make sure to mask the sensitive variables (GCP_SERVICE_ACCOUNT_KEY and BILLOMAT_API_KEY)

## Step 5: Configure GitLab CI/CD Pipeline

The repository already includes a `.gitlab-ci.yml` file that defines the CI/CD pipeline. This pipeline consists of the following stages:

1. **Test**: Runs unit tests and linting
2. **Build**: Prepares the deployment package and runs Terraform plan
3. **Deploy**: Deploys the infrastructure to Google Cloud Platform (requires manual approval)

## Step 6: Run the Pipeline

1. Go to "CI/CD" > "Pipelines" in your GitLab project
2. The pipeline should run automatically when you push to the main branch
3. If you want to run it manually, click "Run Pipeline"
4. Select the branch and click "Run Pipeline"

## Step 7: Monitor the Pipeline

1. Click on the pipeline to see its progress
2. Check the logs of each job for details
3. If any job fails, check the logs for error messages

## Step 8: Approve Deployment

The deployment stage requires manual approval to prevent accidental deployments:

1. When the pipeline reaches the "deploy_to_gcp" job, it will wait for manual approval
2. Click the "Play" button to approve and start the deployment
3. Monitor the deployment logs for any issues

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Check that the GCP_SERVICE_ACCOUNT_KEY is correctly set and the service account has the necessary permissions.

2. **Missing Variables**: Ensure all required CI/CD variables are set correctly.

3. **Terraform Errors**: Check the Terraform logs for specific error messages. Common issues include:
   - Missing permissions
   - Resource naming conflicts
   - Quota limits

4. **Test Failures**: Check the test logs for specific error messages and fix the failing tests.

### Getting Help

If you encounter issues that you cannot resolve, check:
- Google Cloud Platform documentation
- Terraform documentation
- GitLab CI/CD documentation
- Project-specific documentation

## Security Considerations

1. **Protect Your Credentials**: Never commit API keys or service account keys to the repository.

2. **Use Masked Variables**: Make sure sensitive variables are masked in GitLab CI/CD.

3. **Limit Service Account Permissions**: Give the service account only the permissions it needs.

4. **Review Deployment Changes**: Always review the Terraform plan before approving deployment.

## Conclusion

Following these steps will set up a complete CI/CD pipeline for your Billomat Data Integration project. The pipeline will automatically test, build, and deploy your code to Google Cloud Platform, ensuring a smooth and reliable deployment process.
