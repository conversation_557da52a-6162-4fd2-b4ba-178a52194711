# Testing the Billomat Data Integration Project

This directory contains tests for the Billomat data integration project. The tests validate the functionality of retrieving data from Billomat API, processing it, and storing it in Google Cloud Storage.

## Test Structure

- `e2e/`: End-to-end tests that validate the complete workflow
  - `test_billomat_e2e.py`: Comprehensive end-to-end tests for the entire project

## Running Tests

### Prerequisites

Before running the tests, make sure you have installed all the required dependencies:

```bash
pip install -r requirements.txt
```

### Running All Tests

To run all tests, use the provided script:

```bash
./run_tests.sh
```

### Running Specific Tests

To run specific test files:

```bash
python -m unittest tests/e2e/test_billomat_e2e.py
```

To run a specific test method:

```bash
python -m unittest tests.e2e.test_billomat_e2e.TestBillomatE2E.test_complete_workflow
```

## Test Coverage

The tests cover the following functionality:

1. **Billomat API Client**
   - Retrieving invoices, credit notes, articles, and clients
   - Handling PDF attachments
   - Error handling and retries

2. **Data Processing**
   - Transforming XML data to structured JSON
   - Processing invoice data with PDF metadata
   - Enhanced credit notes with customer names
   - Client data retrieval from last 3 pages in reverse order

3. **Google Cloud Storage**
   - Storing data in the correct folder structure
   - Tracking invoice payment status
   - Managing metadata for change detection

4. **Complete Workflow**
   - End-to-end validation of the entire process
   - Integration between all components

## Mocking

The tests use mocking to simulate external dependencies:

- Mock Billomat API responses
- Mock Google Cloud Storage
- Mock environment variables

This allows testing without actual API calls or cloud resources.

## Troubleshooting

If you encounter issues running the tests:

1. Check that all dependencies are installed
2. Verify that the project structure matches the imports in the tests
3. Ensure environment variables are properly set up
4. Check for any import errors in the console output
