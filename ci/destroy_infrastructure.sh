#!/bin/bash
# Script to destroy infrastructure using Terraform and direct GCP API calls

set -e  # Exit on error

# Activate virtual environment if it exists
if [ -d "/tmp/venv" ]; then
  echo "Activating Python virtual environment..."
  source /tmp/venv/bin/activate
fi

# Check if required environment variables are set
if [ -z "$GCP_PROJECT_ID" ]; then
  echo "Error: GCP_PROJECT_ID environment variable is not set."
  exit 1
fi

if [ -z "$GCP_REGION" ]; then
  echo "Error: GCP_REGION environment variable is not set."
  exit 1
fi

if [ -z "$GCS_BUCKET_NAME" ]; then
  echo "Error: GCS_BUCKET_NAME environment variable is not set."
  exit 1
fi

if [ -z "$BILLOMAT_API_KEY" ]; then
  echo "Error: BILLOMAT_API_KEY environment variable is not set."
  exit 1
fi

if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
  echo "Error: GOOGLE_APPLICATION_CREDENTIALS environment variable is not set."
  exit 1
fi

# Navigate to the terraform directory
cd terraform || exit 1

# Check if terraform binary is available
if ! command -v terraform &> /dev/null; then
  echo "Error: terraform command not found. Make sure it's installed and in your PATH."
  exit 1
fi

# Initialize Terraform
echo "Initializing Terraform..."
terraform init

# Validate Terraform configuration
echo "Validating Terraform configuration..."
terraform validate

# Run prepare_deployment.sh if it exists (to ensure state consistency)
if [ -f "prepare_deployment.sh" ]; then
  echo "Running prepare_deployment.sh to ensure state consistency..."
  chmod +x prepare_deployment.sh
  bash prepare_deployment.sh
fi

# Try standard Terraform destroy first
echo "Attempting standard Terraform destroy..."
terraform destroy -auto-approve \
  -var="project_id=${GCP_PROJECT_ID}" \
  -var="region=${GCP_REGION}" \
  -var="bucket_name=${GCS_BUCKET_NAME}" \
  -var="billomat_api_key=${BILLOMAT_API_KEY}" || echo "Standard destroy failed, trying direct resource deletion..."

# If Terraform destroy failed, try direct resource deletion using Python script
echo "Directly deleting resources using Python API..."

# Run the Python script to delete resources
if [ -f "../ci/destroy_gcp_resources.py" ]; then
  echo "Running Python script to delete resources..."
  pip install google-api-python-client
  python3 ../ci/destroy_gcp_resources.py --all
else
  echo "Python script not found, falling back to gcloud commands..."

  # Install gcloud if not available
  if ! command -v gcloud &> /dev/null; then
    echo "Installing gcloud CLI..."
    curl -sSL https://sdk.cloud.google.com | bash -s -- --disable-prompts
    export PATH=$PATH:/root/google-cloud-sdk/bin
    gcloud auth activate-service-account --key-file=${GOOGLE_APPLICATION_CREDENTIALS}
    gcloud config set project ${GCP_PROJECT_ID}
  fi

  # Delete Cloud Functions - ONLY complete-data-ingestion
  echo "Deleting Cloud Functions..."
  gcloud functions delete complete-data-ingestion --region=${GCP_REGION} --quiet || true

  # Delete Cloud Scheduler Jobs - ONLY complete-data-ingestion jobs
  echo "Deleting Cloud Scheduler Jobs..."
  gcloud scheduler jobs delete complete-data-ingestion-tuesday --location=${GCP_REGION} --quiet || true
  gcloud scheduler jobs delete complete-data-ingestion-month-end --location=${GCP_REGION} --quiet || true
fi

# Clean up any temporary files
echo "Cleaning up temporary files..."
rm -rf deploy_temp || true
rm -f function-source.zip || true

echo "Infrastructure destruction completed successfully at $(date)"
echo "Cleanup completed successfully"
