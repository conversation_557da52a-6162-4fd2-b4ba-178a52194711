#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to directly delete GCP resources using the Google Cloud APIs.
This is used as a fallback when Terraform destroy doesn't work due to missing state.
"""

import os
import sys
import time
import argparse
from google.cloud import storage
from google.oauth2 import service_account
import googleapiclient.discovery
import json
import subprocess

def setup_credentials():
    """Set up Google Cloud credentials from environment variables."""
    creds_file = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
    if not creds_file:
        print("Error: GOOGLE_APPLICATION_CREDENTIALS environment variable not set")
        sys.exit(1)

    project_id = os.environ.get('GCP_PROJECT_ID')
    if not project_id:
        print("Error: GCP_PROJECT_ID environment variable not set")
        sys.exit(1)

    region = os.environ.get('GCP_REGION')
    if not region:
        print("Error: GCP_REGION environment variable not set")
        sys.exit(1)

    try:
        credentials = service_account.Credentials.from_service_account_file(
            creds_file,
            scopes=["https://www.googleapis.com/auth/cloud-platform"]
        )
        return credentials, project_id, region
    except Exception as e:
        print(f"Error setting up credentials: {e}")
        sys.exit(1)

def delete_cloud_functions(credentials, project_id, region):
    """Delete Cloud Functions in the project."""
    print("Deleting Cloud Functions...")

    try:
        service = googleapiclient.discovery.build('cloudfunctions', 'v1', credentials=credentials)

        # List of function names to delete - ONLY complete-data-ingestion
        function_names = [
            "complete-data-ingestion"
        ]

        for function_name in function_names:
            full_name = f"projects/{project_id}/locations/{region}/functions/{function_name}"
            try:
                print(f"Deleting function: {function_name}")
                request = service.projects().locations().functions().delete(name=full_name)
                response = request.execute()
                print(f"Delete request submitted for {function_name}")

                # Wait for operation to complete
                if 'name' in response:
                    operation_name = response['name']
                    while True:
                        op_request = service.operations().get(name=operation_name)
                        op_response = op_request.execute()
                        if 'done' in op_response and op_response['done']:
                            print(f"Function {function_name} deleted successfully")
                            break
                        print(f"Waiting for {function_name} deletion to complete...")
                        time.sleep(5)
            except Exception as e:
                print(f"Error deleting function {function_name}: {e}")
    except Exception as e:
        print(f"Error accessing Cloud Functions API: {e}")

def delete_scheduler_jobs(credentials, project_id, region):
    """Delete Cloud Scheduler jobs in the project."""
    print("Deleting Cloud Scheduler Jobs...")

    try:
        service = googleapiclient.discovery.build('cloudscheduler', 'v1', credentials=credentials)

        # List of job names to delete - ONLY complete-data-ingestion jobs
        job_names = [
            "complete-data-ingestion-tuesday",
            "complete-data-ingestion-month-end"
        ]

        for job_name in job_names:
            full_name = f"projects/{project_id}/locations/{region}/jobs/{job_name}"
            try:
                print(f"Deleting scheduler job: {job_name}")
                request = service.projects().locations().jobs().delete(name=full_name)
                request.execute()
                print(f"Job {job_name} deleted successfully")
            except Exception as e:
                print(f"Error deleting job {job_name}: {e}")
    except Exception as e:
        print(f"Error accessing Cloud Scheduler API: {e}")

def delete_storage_objects(credentials, project_id):
    """Delete objects in the function source bucket."""
    print("Deleting storage objects...")

    try:
        storage_client = storage.Client(credentials=credentials, project=project_id)

        # List of buckets to clean
        bucket_names = [
            f"{project_id}-function-source",
            "billomat-data-bucket"
        ]

        for bucket_name in bucket_names:
            try:
                print(f"Cleaning bucket: {bucket_name}")
                bucket = storage_client.bucket(bucket_name)
                blobs = bucket.list_blobs()
                for blob in blobs:
                    print(f"Deleting object: {blob.name}")
                    blob.delete()
                print(f"All objects in {bucket_name} deleted successfully")
            except Exception as e:
                print(f"Error cleaning bucket {bucket_name}: {e}")
    except Exception as e:
        print(f"Error accessing Storage API: {e}")

def main():
    """Main function to delete GCP resources."""
    parser = argparse.ArgumentParser(description='Delete GCP resources directly using APIs')
    parser.add_argument('--all', action='store_true', help='Delete all resource types')
    parser.add_argument('--functions', action='store_true', help='Delete Cloud Functions')
    parser.add_argument('--scheduler', action='store_true', help='Delete Cloud Scheduler jobs')
    parser.add_argument('--storage', action='store_true', help='Delete Storage objects')

    args = parser.parse_args()

    # If no specific resource type is specified, delete all
    if not (args.functions or args.scheduler or args.storage):
        args.all = True

    credentials, project_id, region = setup_credentials()

    if args.all or args.functions:
        delete_cloud_functions(credentials, project_id, region)

    if args.all or args.scheduler:
        delete_scheduler_jobs(credentials, project_id, region)

    if args.all or args.storage:
        delete_storage_objects(credentials, project_id)

    print("Resource deletion completed successfully")

if __name__ == "__main__":
    main()
