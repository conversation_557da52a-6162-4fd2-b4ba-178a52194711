#!/usr/bin/env python3
"""
<PERSON>ript to fix unused imports in Python files.
This script uses the pylint output to identify and remove unused imports.
"""

import os
import re
import subprocess
import sys
from pathlib import Path

def get_unused_imports():
    """Run pylint to get unused imports"""
    try:
        result = subprocess.run(
            ["pylint", "--disable=all", "--enable=unused-import", "src/"],
            capture_output=True,
            text=True,
            check=False
        )
        return result.stdout
    except Exception as e:
        print(f"Error running pylint: {e}")
        return ""

def parse_pylint_output(output):
    """Parse pylint output to get file paths and unused imports"""
    unused_imports = {}
    current_file = None
    
    # Regular expression to match pylint output for unused imports
    file_pattern = re.compile(r'\*{10} Module (.*)')
    import_pattern = re.compile(r'.*: W0611: Unused (.*) imported from (.*) \(unused-import\)')
    
    for line in output.split('\n'):
        file_match = file_pattern.match(line)
        if file_match:
            current_file = file_match.group(1).replace('.', '/') + '.py'
            if current_file not in unused_imports:
                unused_imports[current_file] = []
        
        import_match = import_pattern.match(line)
        if import_match and current_file:
            unused_import = import_match.group(1)
            module = import_match.group(2)
            unused_imports[current_file].append((unused_import, module))
    
    return unused_imports

def fix_file(file_path, unused_imports_list):
    """Fix unused imports in a file"""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    fixed_lines = []
    skip_lines = []
    
    # Process each line
    for i, line in enumerate(lines):
        # Skip lines that have already been marked for removal
        if i in skip_lines:
            continue
        
        # Check if this line is an import statement with unused imports
        for unused_import, module in unused_imports_list:
            # Handle different import formats
            
            # Case 1: from module import unused_import
            if re.match(rf'from\s+{re.escape(module)}\s+import\s+.*{re.escape(unused_import)}.*', line):
                # If it's a multi-import line like "from module import used, unused"
                if ',' in line:
                    # Remove only the unused import
                    parts = line.split('import', 1)
                    if len(parts) == 2:
                        imports = parts[1].split(',')
                        filtered_imports = [imp for imp in imports if unused_import not in imp]
                        if filtered_imports:
                            # If there are still imports left, keep the line with the used imports
                            line = f"{parts[0]}import {','.join(filtered_imports)}\n"
                        else:
                            # If all imports are unused, skip this line
                            continue
                else:
                    # If it's a single import line, skip it entirely
                    continue
            
            # Case 2: import module.unused_import
            elif re.match(rf'import\s+{re.escape(module)}\.{re.escape(unused_import)}', line):
                continue
            
            # Case 3: from typing import Dict, List, Optional, etc.
            elif 'from typing import' in line and unused_import in line:
                parts = line.split('import', 1)
                if len(parts) == 2:
                    imports = parts[1].split(',')
                    filtered_imports = [imp for imp in imports if unused_import not in imp]
                    if filtered_imports:
                        line = f"{parts[0]}import {','.join(filtered_imports)}\n"
                    else:
                        continue
        
        fixed_lines.append(line)
    
    # Write the fixed content back to the file
    with open(file_path, 'w') as f:
        f.writelines(fixed_lines)
    
    print(f"Fixed unused imports in {file_path}")

def main():
    """Main function"""
    print("Running pylint to find unused imports...")
    pylint_output = get_unused_imports()
    
    if not pylint_output:
        print("No unused imports found or pylint failed to run.")
        return
    
    print("Parsing pylint output...")
    unused_imports_by_file = parse_pylint_output(pylint_output)
    
    print(f"Found unused imports in {len(unused_imports_by_file)} files.")
    
    for file_path, unused_imports in unused_imports_by_file.items():
        print(f"Fixing {file_path}...")
        fix_file(file_path, unused_imports)
    
    print("Done!")

if __name__ == "__main__":
    main()
