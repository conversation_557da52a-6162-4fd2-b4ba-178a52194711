#!/bin/bash
# Script to install dependencies based on the Python version

# Get Python version
PYTHON_VERSION=$(python -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')
echo "Detected Python version: $PYTHON_VERSION"

# Create a temporary requirements file
TMP_REQUIREMENTS=$(mktemp)

# Check if Python version is 3.10 or higher
# Using a simple comparison since bc might not be available in all environments
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)

if [[ "$PYTHON_MAJOR" -gt 3 || ("$PYTHON_MAJOR" -eq 3 && "$PYTHON_MINOR" -ge 13) ]]; then
    echo "Using original requirements for Python $PYTHON_VERSION"
    # Use the original requirements file
    cp requirements.txt $TMP_REQUIREMENTS
elif [[ "$PYTHON_MAJOR" -eq 3 && "$PYTHON_MINOR" -eq 11 ]]; then
    echo "Using Python 3.11 specific requirements file"
    # Use the Python 3.11 specific requirements file if it exists
    if [ -f "requirements-py311.txt" ]; then
        cp requirements-py311.txt $TMP_REQUIREMENTS
    else
        echo "requirements-py311.txt not found, creating compatible requirements"
        # Create a compatible requirements file for Python 3.11
        cat requirements.txt | sed \
            -e 's/numpy==2.2.2/numpy==1.26.4/' \
            -e 's/pandas==2.2.3/pandas==2.1.4/' \
            -e 's/pillow==11.1.0/pillow==10.2.0/' \
            -e 's/openpyxl==3.1.5/openpyxl==3.1.2/' \
            > $TMP_REQUIREMENTS
        # Add setuptools to the requirements
        echo "setuptools>=65.5.1" >> $TMP_REQUIREMENTS
    fi
elif [[ "$PYTHON_MAJOR" -eq 3 && "$PYTHON_MINOR" -eq 9 ]]; then
    echo "Using Python 3.9 specific requirements file"
    # Use the Python 3.9 specific requirements file if it exists
    if [ -f "requirements-py39.txt" ]; then
        cp requirements-py39.txt $TMP_REQUIREMENTS
    else
        echo "requirements-py39.txt not found, creating compatible requirements"
        # Create a compatible requirements file for Python 3.9
        cat requirements.txt | sed \
            -e 's/numpy==2.2.2/numpy==1.24.4/' \
            -e 's/pandas==2.2.3/pandas==1.5.3/' \
            -e 's/pillow==11.1.0/pillow==9.5.0/' \
            -e 's/openpyxl==3.1.5/openpyxl==3.1.2/' \
            > $TMP_REQUIREMENTS
        # Add setuptools to the requirements
        echo "setuptools>=65.5.1" >> $TMP_REQUIREMENTS
    fi
else
    echo "Creating compatible requirements for Python $PYTHON_VERSION"
    # Create a compatible requirements file for older Python versions
    cat requirements.txt | sed \
        -e 's/numpy==2.2.2/numpy==1.24.4/' \
        -e 's/pandas==2.2.3/pandas==1.5.3/' \
        -e 's/pillow==11.1.0/pillow==9.5.0/' \
        -e 's/openpyxl==3.1.5/openpyxl==3.1.2/' \
        > $TMP_REQUIREMENTS
fi

# Install dependencies
echo "Installing dependencies..."
pip install --cache-dir=.pip-cache -r $TMP_REQUIREMENTS

# Clean up
rm $TMP_REQUIREMENTS

echo "Dependencies installed successfully!"
