#!/bin/bash
# Script to deploy to Google Cloud Platform using Terraform

set -e  # Exit on error

# Activate virtual environment if it exists
if [ -d "/tmp/venv" ]; then
  echo "Activating Python virtual environment..."
  source /tmp/venv/bin/activate
fi

# Check if required environment variables are set
if [ -z "$GCP_PROJECT_ID" ]; then
  echo "Error: GCP_PROJECT_ID environment variable is not set."
  exit 1
fi

if [ -z "$GCP_REGION" ]; then
  echo "Error: GCP_REGION environment variable is not set."
  exit 1
fi

if [ -z "$BILLOMAT_API_KEY" ]; then
  echo "Error: BILLOMAT_API_KEY environment variable is not set."
  exit 1
fi

if [ -z "$GCS_BUCKET_NAME" ]; then
  echo "Error: GCS_BUCKET_NAME environment variable is not set."
  exit 1
fi

if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
  echo "Error: GOOGLE_APPLICATION_CREDENTIALS environment variable is not set."
  exit 1
fi

# Navigate to the terraform directory
cd terraform || exit 1

# Check if terraform binary is available
if ! command -v terraform &> /dev/null; then
  echo "Error: terraform command not found. Make sure it's installed and in your PATH."
  exit 1
fi

# Initialize Terraform
echo "Initializing Terraform..."
terraform init

# Validate Terraform configuration
echo "Validating Terraform configuration..."
terraform validate

# Run prepare_deployment.sh if it exists
if [ -f "prepare_deployment.sh" ]; then
  echo "Running prepare_deployment.sh..."
  chmod +x prepare_deployment.sh
  bash prepare_deployment.sh
else
  echo "prepare_deployment.sh not found, using existing function-source.zip"
fi

# Check if tfplan exists
if [ -f "tfplan" ] && [ -s "tfplan" ]; then
  echo "Found tfplan file, applying Terraform plan..."
  terraform apply -auto-approve tfplan
else
  echo "tfplan is empty or doesn't exist, running terraform apply directly..."
  terraform apply -auto-approve \
    -var="project_id=${GCP_PROJECT_ID}" \
    -var="region=${GCP_REGION}" \
    -var="bucket_name=${GCS_BUCKET_NAME}" \
    -var="billomat_api_key=${BILLOMAT_API_KEY}"
fi

echo "Deployment process completed successfully"
