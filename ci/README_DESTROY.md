# Infrastructure Destruction Guide

This document explains how to use the infrastructure destruction job in the GitLab CI/CD pipeline.

## Overview

The `destroy_infrastructure` job is designed to safely and completely remove all "Complete Data Ingestion" resources from Google Cloud Platform. This includes:

- Cloud Function: `complete-data-ingestion`
- Cloud Scheduler Jobs:
  - `complete-data-ingestion-tuesday`
  - `complete-data-ingestion-month-end`

## How It Works

The destruction process uses a multi-layered approach to ensure all resources are properly removed:

1. **Terraform Destroy**: First attempts to use Terraform to destroy resources based on the current configuration
2. **Python API Deletion**: If Terraform fails, uses the Google Cloud APIs directly via a Python script
3. **gcloud CLI Commands**: As a final fallback, uses gcloud CLI commands to delete resources

## How to Use

1. Go to your GitLab CI/CD pipeline
2. Find the `destroy_infrastructure` job in the pipeline view
3. Click the "Play" button to manually trigger the job
4. The job will run and attempt to delete all "Complete Data Ingestion" resources
5. Check the job logs to confirm successful deletion

## Important Notes

- This job will **only** delete resources with the "complete-data-ingestion" prefix
- It will **not** delete any "billomat-ingestion" resources
- The job is designed to be run manually for safety
- You can run this job even if the resources were created in a previous pipeline run

## Troubleshooting

If the job fails to delete some resources, check the logs for specific error messages. Common issues include:

- **Permission Issues**: Ensure the service account has the necessary permissions
- **Resource Not Found**: The resource may have already been deleted
- **Dependency Issues**: Some resources may have dependencies that need to be deleted first

## Manual Cleanup

If the automatic cleanup fails, you can manually delete resources using the Google Cloud Console:

1. Go to the Google Cloud Console
2. Navigate to Cloud Functions and delete `complete-data-ingestion`
3. Navigate to Cloud Scheduler and delete the scheduler jobs
4. Check Storage buckets for any remaining artifacts

## Questions or Issues

If you encounter any issues with the destruction process, please contact the DevOps team for assistance.
