#!/bin/bash
# Script to prepare the CI/CD environment

# Create necessary directories
mkdir -p .pip-cache
mkdir -p terraform/deploy_temp

# Make scripts executable (if they exist)
if [ -f "terraform/prepare_deployment.sh" ]; then
  chmod +x terraform/prepare_deployment.sh
  echo "Made terraform/prepare_deployment.sh executable"
else
  echo "Warning: terraform/prepare_deployment.sh not found"
fi

if [ -f "run_tests.sh" ]; then
  chmod +x run_tests.sh
  echo "Made run_tests.sh executable"
else
  echo "Warning: run_tests.sh not found"
fi

if [ -f "ci/authenticate_gcp.sh" ]; then
  chmod +x ci/authenticate_gcp.sh
  echo "Made ci/authenticate_gcp.sh executable"
else
  echo "Warning: ci/authenticate_gcp.sh not found"
fi

if [ -f "ci/deploy_to_gcp.sh" ]; then
  chmod +x ci/deploy_to_gcp.sh
  echo "Made ci/deploy_to_gcp.sh executable"
else
  echo "Warning: ci/deploy_to_gcp.sh not found"
fi

# Verify environment variables
echo "Checking required environment variables..."
REQUIRED_VARS=("GCP_PROJECT_ID" "GCP_REGION" "GCS_BUCKET_NAME" "BILLOMAT_API_KEY" "GCP_SERVICE_ACCOUNT_KEY")
MISSING_VARS=0

for VAR in "${REQUIRED_VARS[@]}"; do
  if [ -z "${!VAR}" ]; then
    echo "Warning: $VAR is not set"
    MISSING_VARS=$((MISSING_VARS+1))
  else
    echo "$VAR is set"
  fi
done

if [ $MISSING_VARS -gt 0 ]; then
  echo "Warning: $MISSING_VARS required environment variables are missing"
  echo "This may cause issues in later stages, but continuing for now..."
fi

# Create empty files if they don't exist (for testing purposes)
if [ ! -d "src" ]; then
  echo "Creating src directory structure for testing..."
  mkdir -p src/cloud_functions/ingestions/billomat
  mkdir -p src/data_pipelines/billomat
  mkdir -p src/infrastructure/gcs/storage_manager
  touch src/cloud_functions/ingestions/billomat/main.py
  touch src/data_pipelines/billomat/client.py
  touch src/data_pipelines/billomat/processor.py
  touch src/infrastructure/gcs/storage_manager/billomat_storage.py
fi

echo "CI/CD environment prepared successfully!"
