#!/bin/bash
# <PERSON><PERSON>t to authenticate with Google Cloud Platform in CI/CD environment

# Check if the service account key is provided
if [ -z "$GCP_SERVICE_ACCOUNT_KEY" ]; then
  echo "Error: GCP_SERVICE_ACCOUNT_KEY environment variable is not set."
  exit 1
fi

# Create the credentials file
echo "$GCP_SERVICE_ACCOUNT_KEY" > ${GOOGLE_APPLICATION_CREDENTIALS}
chmod 600 ${GOOGLE_APPLICATION_CREDENTIALS}

# Install the Google Cloud SDK
if command -v apt-get &> /dev/null; then
  echo "Installing Google Cloud SDK via apt..."
  apt-get update && apt-get install -y apt-transport-https ca-certificates gnupg curl
  echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
  curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
  apt-get update && apt-get install -y google-cloud-sdk
elif command -v pip &> /dev/null; then
  echo "Installing Google Cloud authentication libraries..."
  pip install --no-cache-dir google-auth google-auth-oauthlib google-auth-httplib2 google-cloud-sdk || echo "Failed to install Google Cloud libraries, but continuing..."
fi

# Set environment variable for authentication
export GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}

# Authenticate with Google Cloud
if command -v gcloud &> /dev/null; then
  echo "Authenticating with gcloud..."
  gcloud auth activate-service-account --key-file=${GOOGLE_APPLICATION_CREDENTIALS}
  gcloud config set project ${GCP_PROJECT_ID}
  gcloud config set compute/region ${GCP_REGION}
else
  echo "gcloud not available, using service account key directly..."
fi

echo "Successfully authenticated with Google Cloud Platform"
