#!/bin/bash
# Script to install Google Cloud SDK on Alpine Linux

echo "Installing Google Cloud SDK on Alpine Linux..."

# Install dependencies
apk add --no-cache python3 py3-pip curl bash

# Create a simple authentication script that uses the service account key directly
cat > /usr/local/bin/gcloud << 'EOF'
#!/bin/bash
# This is a simplified gcloud wrapper that just sets the authentication
echo "Using service account key for authentication..."
export GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}
echo "Authentication set via GOOGLE_APPLICATION_CREDENTIALS"
EOF

# Make it executable
chmod +x /usr/local/bin/gcloud

echo "Simplified gcloud wrapper created for CI/CD environment"
