"""
Modified BillomatWorkflow class for use with the Cloud Function
"""
import os
from datetime import datetime, timedelta
from typing import Optional
import xml.etree.ElementTree as ET

from data_pipelines.billomat.client import BillomatClient
from data_pipelines.billomat.processor import BillomatDataProcessor
from infrastructure.gcs.storage_manager.billomat_storage import BillomatGCSStorage
from infrastructure.monitoring.logging import setup_logger

logger = setup_logger(__name__)

class BillomatWorkflow:
    def __init__(self, api_key=None):
        """
        Initialize the Billomat workflow with optional API key.
        
        Args:
            api_key (str, optional): Billomat API key. If None, gets from environment.
        """
        # Get API key from parameter or environment variable
        self.api_key = api_key or os.getenv('BILLOMAT_API_KEY')
        if not self.api_key:
            raise ValueError("BILLOMAT_API_KEY environment variable not set")
            
        # Initialize components
        self.client = BillomatClient(self.api_key)
        self.processor = BillomatDataProcessor()
        self.storage = BillomatGCSStorage()

    def process_invoices(self, from_date: Optional[str] = None, to_date: Optional[str] = None):
        """Process and store invoices."""
        try:
            logger.info(f"Processing invoices from {from_date} to {to_date}")
            
            # Get invoices
            invoices_data = self.client.get_invoices(from_date, to_date)
            root = ET.fromstring(invoices_data)
    
            invoice_count = 0
            for invoice_elem in root.findall('.//invoice'):
                try:
                    invoice_id = invoice_elem.find('id').text
                    logger.info(f"Processing invoice {invoice_id}")
                    detailed_data = self.client.get_invoice(invoice_id)
                    pdf_data = self.client.get_invoice_pdf(invoice_id)
                    
                    processed_data = self.processor.process_data(
                        detailed_data,
                        data_type='invoice',
                        pdf_metadata=pdf_data.get('metadata') if pdf_data else None
                    )
                    
                    # Store data
                    self.storage.store_invoice_complete(processed_data, pdf_data)
                    
                    logger.info(f"Successfully processed invoice {invoice_id}")
                    invoice_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing invoice {invoice_id}: {str(e)}")
                    continue
            
            logger.info(f"Processed {invoice_count} invoices")
                    
        except Exception as e:
            logger.error(f"Error in invoice processing: {str(e)}")
            raise

    def process_credit_notes(self, from_date: Optional[str] = None, to_date: Optional[str] = None):
        """Process and store credit notes."""
        try:
            logger.info(f"Processing credit notes from {from_date} to {to_date}")
            
            credit_notes_data = self.client.get_credit_notes(from_date, to_date)
            root = ET.fromstring(credit_notes_data)
            
            credit_note_count = 0
            for credit_note_elem in root.findall('.//credit-note'):
                try:
                    credit_note_id = credit_note_elem.find('id').text
                    logger.info(f"Processing credit note {credit_note_id}")
                    detailed_data = self.client.get_credit_note(credit_note_id)
                    
                    processed_data = self.processor.process_credit_note(detailed_data)
                    self.storage.store_credit_note(processed_data)
                    
                    logger.info(f"Successfully processed credit note {credit_note_id}")
                    credit_note_count += 1
                    
                except Exception as e:
                    logger.error(f"Error processing credit note {credit_note_id}: {str(e)}")
                    continue
            
            logger.info(f"Processed {credit_note_count} credit notes")
                    
        except Exception as e:
            logger.error(f"Error in credit notes processing: {str(e)}")
            raise

    def process_clients(self):
        """Process and store clients data."""
        try:
            logger.info("Processing clients")
            
            clients_data = self.client.get_clients()
            processed_data = self.processor.process_clients(clients_data)
            self.storage.store_clients(processed_data)
            
            logger.info(f"Successfully processed {len(processed_data['data'].get('clients', []))} clients")
            
        except Exception as e:
            logger.error(f"Error in clients processing: {str(e)}")
            raise

    def process_articles(self):
        """Process and store articles data."""
        try:
            logger.info("Processing articles")
            
            articles_data = self.client.get_articles()
            processed_data = self.processor.process_articles(articles_data)
            self.storage.store_articles(processed_data)
            
            logger.info(f"Successfully processed {len(processed_data['data'].get('articles', []))} articles")
            
        except Exception as e:
            logger.error(f"Error in articles processing: {str(e)}")
            raise

    def run(self, from_date: Optional[str] = None, to_date: Optional[str] = None):
        """
        Run the complete workflow.
        
        Args:
            from_date (Optional[str]): Start date in YYYY-MM-DD format
            to_date (Optional[str]): End date in YYYY-MM-DD format
        """
        try:
            logger.info("Starting Billomat data processing workflow")
            
            # If dates not provided, use last 2 days
            if not from_date or not to_date:
                to_date_dt = datetime.now()
                from_date_dt = to_date_dt - timedelta(days=2)
                
                from_date = from_date or from_date_dt.strftime('%Y-%m-%d')
                to_date = to_date or to_date_dt.strftime('%Y-%m-%d')
                
                logger.info(f"Date range automatically set: {from_date} to {to_date}")
            
            # Process all data types
            self.process_invoices(from_date, to_date)
            self.process_credit_notes(from_date, to_date)
            self.process_clients()
            self.process_articles()
            
            logger.info("Completed Billomat data processing workflow")
            
        except Exception as e:
            logger.error(f"Error in workflow execution: {str(e)}")
            raise