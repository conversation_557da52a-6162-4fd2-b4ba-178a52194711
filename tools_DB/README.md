# MySQL Database Synchronization from AWS to Google Cloud Platform

## Project Overview

This project implements a robust data synchronization system to regularly retrieve MySQL database data from an AWS server and store it efficiently in Google Cloud Platform (GCP). The system is designed to handle structured data from a tourism/accommodation management system with tables for accommodations, activities, bookings, and financial data.

## Architecture Decision: Compute Engine vs Cloud Run Functions

### Current Setup Analysis
Your existing system uses:
- Google Cloud Run Functions for data processing
- Google Cloud Storage for data storage  
- Google Cloud Scheduler for automation
- Parquet files for structured data storage
- JSON format for refined trip and finance datasets

### Recommendation: **Use Google Compute Engine**

After analyzing your requirements, **Google Compute Engine is the recommended approach** for the following reasons:

#### Why Compute Engine is Better for Your Use Case:

1. **Database Connection Persistence**
   - Maintains persistent connections to AWS MySQL server
   - Reduces connection overhead for large data transfers
   - Better handling of long-running database operations

2. **Memory and Processing Power**
   - Can handle large datasets without timeout constraints
   - Better memory management for data transformation operations
   - Supports complex data processing workflows

3. **Cost Efficiency for Regular Operations**
   - More cost-effective for scheduled, predictable workloads
   - No cold start delays like Cloud Functions
   - Better resource utilization for batch processing

4. **Flexibility and Control**
   - Full control over the execution environment
   - Can install and configure specific database tools
   - Better error handling and retry mechanisms
   - Support for complex data validation and transformation

#### When Cloud Run Functions Would Be Better:
- Sporadic, event-driven data retrieval
- Simple, lightweight data processing
- Serverless architecture preference
- Minimal maintenance requirements

## Detailed Implementation Plan

### Phase 1: Infrastructure Setup

#### 1.1 Google Compute Engine Configuration

**VM Specifications:**
- **Machine Type**: e2-standard-2 (2 vCPUs, 8GB RAM)
- **Boot Disk**: 50GB SSD persistent disk
- **Operating System**: Ubuntu 20.04 LTS
- **Region**: Choose based on your primary user base (e.g., us-central1, europe-west1)

**Estimated Monthly Cost:**
- VM Instance: ~$50-70/month
- Storage: ~$8-10/month  
- Network Egress: ~$5-15/month (depending on data volume)
- **Total Estimated Cost: $65-95/month**

#### 1.2 Security Configuration

```bash
# Create firewall rules
gcloud compute firewall-rules create mysql-sync-ssh \
    --allow tcp:22 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow SSH access"

# Create service account with minimal permissions
gcloud iam service-accounts create mysql-sync-service \
    --description="Service account for MySQL sync operations" \
    --display-name="MySQL Sync Service"
```

#### 1.3 VM Creation Script

```bash
#!/bin/bash
# Create the VM instance
gcloud compute instances create mysql-sync-vm \
    --zone=us-central1-a \
    --machine-type=e2-standard-2 \
    --network-interface=network-tier=PREMIUM,subnet=default \
    --maintenance-policy=MIGRATE \
    --provisioning-model=STANDARD \
    --service-account=mysql-sync-service@PROJECT_ID.iam.gserviceaccount.com \
    --scopes=https://www.googleapis.com/auth/cloud-platform \
    --create-disk=auto-delete=yes,boot=yes,device-name=mysql-sync-vm,image=projects/ubuntu-os-cloud/global/images/ubuntu-2004-focal-v20231213,mode=rw,size=50,type=projects/PROJECT_ID/zones/us-central1-a/diskTypes/pd-ssd \
    --no-shielded-secure-boot \
    --shielded-vtpm \
    --shielded-integrity-monitoring \
    --reservation-affinity=any
```

### Phase 2: Environment Setup

#### 2.1 VM Initial Configuration

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Python and required packages
sudo apt install -y python3 python3-pip python3-venv mysql-client

# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
gcloud init

# Create project directory
mkdir -p /opt/mysql-sync
cd /opt/mysql-sync

# Create Python virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install pymysql pandas pyarrow google-cloud-storage google-cloud-logging schedule
```

#### 2.2 Directory Structure

```
/opt/mysql-sync/
├── config/
│   ├── database.conf
│   └── gcp.conf
├── src/
│   ├── database/
│   │   ├── __init__.py
│   │   ├── mysql_client.py
│   │   └── data_extractor.py
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── gcs_manager.py
│   │   └── data_transformer.py
│   ├── scheduler/
│   │   ├── __init__.py
│   │   └── sync_scheduler.py
│   └── utils/
│       ├── __init__.py
│       ├── logging_config.py
│       └── config_manager.py
├── logs/
├── temp/
├── scripts/
│   ├── setup.sh
│   ├── sync_runner.py
│   └── health_check.py
└── requirements.txt
```

### Phase 3: Application Development

#### 3.1 Database Connection Module

```python
"""
MySQL Database Client for AWS Connection
Handles secure connection and data extraction
"""

import pymysql
import pandas as pd
from typing import Dict, List, Optional
import logging
from contextlib import contextmanager

class MySQLClient:
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    @contextmanager
    def get_connection(self):
        """
        Context manager for database connections
        Ensures proper connection cleanup
        """
        connection = None
        try:
            connection = pymysql.connect(
                host=self.config['host'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                connect_timeout=30,
                read_timeout=300,
                write_timeout=300
            )
            yield connection
        except Exception as e:
            self.logger.error(f"Database connection error: {e}")
            raise
        finally:
            if connection:
                connection.close()
    
    def extract_table_data(self, table_name: str, 
                          date_column: Optional[str] = None,
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None) -> pd.DataFrame:
        """
        Extract data from specified table with optional date filtering
        """
        query = f"SELECT * FROM {table_name}"
        
        if date_column and start_date:
            query += f" WHERE {date_column} >= '{start_date}'"
            if end_date:
                query += f" AND {date_column} <= '{end_date}'"
        
        with self.get_connection() as conn:
            return pd.read_sql(query, conn)
```

#### 3.2 Data Storage and Transformation

```python
"""
Google Cloud Storage Manager
Handles data upload and organization
"""

from google.cloud import storage
import pandas as pd
import json
from datetime import datetime
import os

class GCSManager:
    def __init__(self, bucket_name: str, project_id: str):
        self.bucket_name = bucket_name
        self.project_id = project_id
        self.client = storage.Client(project=project_id)
        self.bucket = self.client.bucket(bucket_name)
    
    def save_as_parquet(self, df: pd.DataFrame, 
                       table_name: str, 
                       sync_date: str) -> str:
        """
        Save DataFrame as Parquet file in GCS
        Organized by table and date
        """
        file_path = f"raw_data/{table_name}/{sync_date}/{table_name}.parquet"
        
        # Save locally first
        local_path = f"/tmp/{table_name}.parquet"
        df.to_parquet(local_path, engine='pyarrow', compression='snappy')
        
        # Upload to GCS
        blob = self.bucket.blob(file_path)
        blob.upload_from_filename(local_path)
        
        # Cleanup local file
        os.remove(local_path)
        
        return f"gs://{self.bucket_name}/{file_path}"
    
    def save_processed_json(self, data: Dict, 
                           data_type: str, 
                           sync_date: str) -> str:
        """
        Save processed data as JSON for trips and finance datasets
        """
        file_path = f"processed_data/{data_type}/{sync_date}/{data_type}_processed.json"
        
        # Save locally first
        local_path = f"/tmp/{data_type}_processed.json"
        with open(local_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        # Upload to GCS
        blob = self.bucket.blob(file_path)
        blob.upload_from_filename(local_path)
        
        # Cleanup local file
        os.remove(local_path)
        
        return f"gs://{self.bucket_name}/{file_path}"
```

### Phase 4: Scheduling and Automation

#### 4.1 Sync Scheduler

```python
"""
Automated sync scheduler
Handles regular data synchronization
"""

import schedule
import time
from datetime import datetime, timedelta
import logging

class SyncScheduler:
    def __init__(self, mysql_client, gcs_manager):
        self.mysql_client = mysql_client
        self.gcs_manager = gcs_manager
        self.logger = logging.getLogger(__name__)
    
    def full_sync(self):
        """
        Perform full database synchronization
        """
        try:
            sync_date = datetime.now().strftime('%Y-%m-%d')
            self.logger.info(f"Starting full sync for {sync_date}")
            
            # Define tables to sync
            tables_config = {
                'accommodations': {'date_column': 'updated_at'},
                'activities': {'date_column': 'updated_at'},
                'bookings': {'date_column': 'created_at'},
                'payments': {'date_column': 'payment_date'},
                'customers': {'date_column': 'updated_at'}
            }
            
            for table_name, config in tables_config.items():
                self.logger.info(f"Syncing table: {table_name}")
                
                # Extract data
                df = self.mysql_client.extract_table_data(
                    table_name=table_name,
                    date_column=config.get('date_column'),
                    start_date=(datetime.now() - timedelta(days=32)).strftime('%Y-%m-%d')
                )
                
                # Save as Parquet
                parquet_path = self.gcs_manager.save_as_parquet(
                    df, table_name, sync_date
                )
                
                self.logger.info(f"Saved {len(df)} records to {parquet_path}")
            
            # Process and save refined data
            self._process_trips_data(sync_date)
            self._process_finance_data(sync_date)
            
            self.logger.info("Full sync completed successfully")
            
        except Exception as e:
            self.logger.error(f"Sync failed: {e}")
            raise
    
    def _process_trips_data(self, sync_date: str):
        """
        Process and refine trips data into JSON format
        """
        # Implementation for trips data processing
        pass
    
    def _process_finance_data(self, sync_date: str):
        """
        Process and refine finance data into JSON format  
        """
        # Implementation for finance data processing
        pass
    
    def start_scheduler(self):
        """
        Start the scheduled sync operations
        """
        # Schedule twice monthly (1st and 15th)
        schedule.every().month.at("02:00").do(self.full_sync)
        schedule.every(15).days.at("02:00").do(self.full_sync)
        
        self.logger.info("Scheduler started - running twice monthly")
        
        while True:
            schedule.run_pending()
            time.sleep(3600)  # Check every hour
```

## Cost Analysis and Comparison

### Google Compute Engine Approach
**Monthly Costs:**
- VM Instance (e2-standard-2): $52-68
- Storage (50GB SSD): $8
- Network Egress: $10-20
- **Total: $70-96/month**

### Cloud Run Functions Approach  
**Monthly Costs:**
- Function Invocations: $5-15
- Compute Time: $10-25
- Network Egress: $10-20
- **Total: $25-60/month**

### Cost-Benefit Analysis

**Compute Engine Advantages:**
- Better performance for large datasets
- More reliable for complex operations
- Persistent connections reduce overhead
- Full control over execution environment

**Cloud Functions Advantages:**
- Lower cost for simple operations
- Serverless management
- Automatic scaling
- Pay-per-use model

## Data Storage Strategy Recommendations

### Current Approach (Recommended to Continue):
1. **Raw Data**: Store in Parquet format for efficient querying and compression
2. **Processed Data**: Store refined trip and finance data in JSON format
3. **Organization**: Hierarchical structure by date and data type

### Enhanced Storage Strategy:
```
gs://your-bucket/
├── raw_data/
│   ├── accommodations/
│   │   └── 2024-01-15/
│   │       └── accommodations.parquet
│   ├── activities/
│   └── bookings/
├── processed_data/
│   ├── trips/
│   │   └── 2024-01-15/
│   │       ├── trips_summary.json
│   │       └── trips_detailed.json
│   └── finance/
│       └── 2024-01-15/
│           ├── revenue_summary.json
│           └── payment_details.json
└── metadata/
    ├── sync_logs/
    └── data_quality_reports/
```

## Implementation Timeline

### Week 1: Infrastructure Setup
- Create GCP Compute Engine instance
- Configure security and networking
- Install required software and dependencies

### Week 2: Application Development
- Develop MySQL client and data extraction modules
- Create GCS storage and transformation components
- Implement logging and error handling

### Week 3: Scheduler and Automation
- Develop sync scheduler with cron-like functionality
- Create health check and monitoring scripts
- Implement data validation and quality checks

### Week 4: Testing and Deployment
- Perform end-to-end testing with sample data
- Optimize performance and error handling
- Deploy production configuration and monitoring

## Monitoring and Maintenance

### Health Checks
- Database connectivity monitoring
- GCS upload success verification
- Data quality validation
- Resource utilization tracking

### Alerting
- Failed sync notifications
- Database connection issues
- Storage quota warnings
- Performance degradation alerts

### Backup Strategy
- Regular VM snapshots
- Configuration backup to GCS
- Database connection credential rotation
- Disaster recovery procedures

## Next Steps

1. **Approve Architecture**: Confirm Compute Engine approach
2. **Set Up GCP Project**: Create dedicated project for database sync
3. **Configure AWS Access**: Ensure network connectivity to AWS MySQL
4. **Begin Implementation**: Start with Phase 1 infrastructure setup
5. **Iterative Development**: Build and test components incrementally

This comprehensive approach ensures reliable, cost-effective, and scalable database synchronization while maintaining data quality and system reliability.
