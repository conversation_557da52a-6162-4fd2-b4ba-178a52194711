-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.6.11-Maria<PERSON><PERSON>, for debian-linux-gnu (x86_64)
--
-- Host: localhost    Database: forge
-- ------------------------------------------------------
-- Server version	10.6.11-MariaDB-1:10.6.11+maria~ubu2004

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `accommodation_categories`
--

DROP TABLE IF EXISTS `accommodation_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` enum('hotel','casa') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodation_language`
--

DROP TABLE IF EXISTS `accommodation_language`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_language` (
  `accommodation_id` bigint(20) unsigned NOT NULL,
  `language_id` bigint(20) unsigned NOT NULL,
  KEY `accommodation_language_accommodation_id_foreign` (`accommodation_id`),
  KEY `accommodation_language_language_id_foreign` (`language_id`),
  CONSTRAINT `accommodation_language_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`),
  CONSTRAINT `accommodation_language_language_id_foreign` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodation_promotion`
--

DROP TABLE IF EXISTS `accommodation_promotion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_promotion` (
  `accommodation_id` bigint(20) unsigned NOT NULL,
  `accommodation_room_id` bigint(20) unsigned DEFAULT NULL,
  `promotion_id` bigint(20) unsigned NOT NULL,
  `all` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  KEY `accommodation_promotion_accommodation_id_foreign` (`accommodation_id`),
  KEY `accommodation_promotion_accommodation_room_id_foreign` (`accommodation_room_id`),
  KEY `accommodation_promotion_promotion_id_foreign` (`promotion_id`),
  CONSTRAINT `accommodation_promotion_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`),
  CONSTRAINT `accommodation_promotion_accommodation_room_id_foreign` FOREIGN KEY (`accommodation_room_id`) REFERENCES `accommodation_rooms` (`id`),
  CONSTRAINT `accommodation_promotion_promotion_id_foreign` FOREIGN KEY (`promotion_id`) REFERENCES `promotions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodation_room_feature`
--

DROP TABLE IF EXISTS `accommodation_room_feature`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_room_feature` (
  `accommodation_room_id` bigint(20) unsigned NOT NULL,
  `accommodation_room_feature_id` bigint(20) unsigned NOT NULL,
  KEY `accommodation_room_feature_accommodation_room_id_foreign` (`accommodation_room_id`),
  KEY `accommodation_room_feature_accommodation_room_feature_id_foreign` (`accommodation_room_feature_id`),
  CONSTRAINT `accommodation_room_feature_accommodation_room_feature_id_foreign` FOREIGN KEY (`accommodation_room_feature_id`) REFERENCES `accommodation_room_features` (`id`),
  CONSTRAINT `accommodation_room_feature_accommodation_room_id_foreign` FOREIGN KEY (`accommodation_room_id`) REFERENCES `accommodation_rooms` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodation_room_features`
--

DROP TABLE IF EXISTS `accommodation_room_features`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_room_features` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `abbreviation` tinytext DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodation_room_prices`
--

DROP TABLE IF EXISTS `accommodation_room_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_room_prices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `price_ai` int(11) DEFAULT NULL,
  `price_cap` int(11) DEFAULT NULL,
  `price_map` int(11) DEFAULT NULL,
  `price_ap` int(11) DEFAULT NULL,
  `price_ro` int(11) DEFAULT NULL,
  `price_single` int(11) DEFAULT NULL,
  `date_start` date NOT NULL,
  `date_end` date NOT NULL,
  `accommodation_room_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accommodation_room_prices_accommodation_room_id_foreign` (`accommodation_room_id`),
  KEY `accommodation_room_prices_date_start_date_end_index` (`date_start`,`date_end`),
  CONSTRAINT `accommodation_room_prices_accommodation_room_id_foreign` FOREIGN KEY (`accommodation_room_id`) REFERENCES `accommodation_rooms` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=81227 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodation_room_types`
--

DROP TABLE IF EXISTS `accommodation_room_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_room_types` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodation_rooms`
--

DROP TABLE IF EXISTS `accommodation_rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodation_rooms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `scoring_category` enum('A','B','C') DEFAULT NULL,
  `number_of_rooms` int(11) DEFAULT NULL,
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `pax_max` int(10) unsigned DEFAULT NULL,
  `room_type` enum('single','double','triple','quadruple') DEFAULT NULL,
  `pax_adults` tinyint(4) NOT NULL,
  `pax_children` tinyint(4) NOT NULL,
  `pax_infants` tinyint(4) NOT NULL,
  `price_per_person` tinyint(1) NOT NULL,
  `discount_third_person` decimal(8,2) DEFAULT NULL,
  `accommodation_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accommodation_rooms_accommodation_id_foreign` (`accommodation_id`),
  KEY `accommodation_rooms_active_room_type_index` (`active`,`room_type`),
  CONSTRAINT `accommodation_rooms_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1853 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `accommodations`
--

DROP TABLE IF EXISTS `accommodations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `accommodations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `default_price_type` varchar(255) DEFAULT NULL,
  `scoring_category` enum('A','B','C') DEFAULT NULL,
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `type` enum('hotel','casa') NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `rating` enum('1_star','2_star','3_star','4_star','5_star','buddy','premium','boutique') DEFAULT NULL,
  `checkout` time DEFAULT NULL,
  `checkin` time DEFAULT NULL,
  `discount_children` decimal(8,2) DEFAULT NULL,
  `discount_infants` decimal(8,2) DEFAULT NULL,
  `proposal_name` text DEFAULT NULL,
  `proposal_description` text DEFAULT NULL,
  `sales_description` text DEFAULT NULL,
  `short_description` text DEFAULT NULL,
  `proposal_highlights` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '(DC2Type:json)' CHECK (json_valid(`proposal_highlights`)),
  `product_catalogue_description` text DEFAULT NULL,
  `video` varchar(255) DEFAULT NULL,
  `location_id` bigint(20) unsigned NOT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `accommodation_category_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `travel_document_description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accommodations_location_id_foreign` (`location_id`),
  KEY `accommodations_currency_id_foreign` (`currency_id`),
  KEY `accommodations_accommodation_category_id_foreign` (`accommodation_category_id`),
  CONSTRAINT `accommodations_accommodation_category_id_foreign` FOREIGN KEY (`accommodation_category_id`) REFERENCES `accommodation_categories` (`id`),
  CONSTRAINT `accommodations_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`),
  CONSTRAINT `accommodations_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=567 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activities`
--

DROP TABLE IF EXISTS `activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `scoring_category` enum('A','B','C') DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `is_only_group_trips` tinyint(1) NOT NULL DEFAULT 0,
  `icon` varchar(255) DEFAULT NULL,
  `time_start` time NOT NULL,
  `time_end` time NOT NULL,
  `duration` int(11) NOT NULL,
  `discount_children` decimal(8,2) DEFAULT NULL,
  `discount_infants` decimal(8,2) DEFAULT NULL,
  `estimated_km` int(11) DEFAULT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `location_id` bigint(20) unsigned NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `features` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `target_group` varchar(255) DEFAULT NULL,
  `proposal_description` text DEFAULT NULL,
  `services_included` text DEFAULT NULL,
  `difficulty` varchar(255) DEFAULT NULL,
  `video` varchar(255) DEFAULT NULL,
  `short_description` text DEFAULT NULL,
  `proposal_highlights` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '(DC2Type:json)' CHECK (json_valid(`proposal_highlights`)),
  `proposal_name` text DEFAULT NULL,
  `product_catalogue_description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `travel_document_description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activities_currency_id_foreign` (`currency_id`),
  KEY `activities_location_id_foreign` (`location_id`),
  CONSTRAINT `activities_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`),
  CONSTRAINT `activities_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=356 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_base_prices`
--

DROP TABLE IF EXISTS `activity_base_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_base_prices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) unsigned NOT NULL,
  `up_to_pax` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activity_base_prices_activity_id_foreign` (`activity_id`),
  CONSTRAINT `activity_base_prices_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9826 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_language`
--

DROP TABLE IF EXISTS `activity_language`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_language` (
  `activity_id` bigint(20) unsigned NOT NULL,
  `language_id` bigint(20) unsigned NOT NULL,
  KEY `activity_language_activity_id_foreign` (`activity_id`),
  KEY `activity_language_language_id_foreign` (`language_id`),
  CONSTRAINT `activity_language_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`),
  CONSTRAINT `activity_language_language_id_foreign` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_parts`
--

DROP TABLE IF EXISTS `activity_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_parts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) unsigned NOT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `cost_model` varchar(255) NOT NULL,
  `price` int(10) unsigned DEFAULT NULL,
  `cost` int(10) unsigned DEFAULT NULL,
  `discount_children` double(8,2) unsigned NOT NULL DEFAULT 0.00,
  `discount_infants` double(8,2) unsigned NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activity_id` (`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1029 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_transfer_stop`
--

DROP TABLE IF EXISTS `activity_transfer_stop`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_transfer_stop` (
  `activity_id` bigint(20) unsigned NOT NULL,
  `transfer_stop_id` bigint(20) unsigned NOT NULL,
  KEY `activity_transfer_stop_activity_id_foreign` (`activity_id`),
  KEY `activity_transfer_stop_transfer_stop_id_foreign` (`transfer_stop_id`),
  CONSTRAINT `activity_transfer_stop_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`),
  CONSTRAINT `activity_transfer_stop_transfer_stop_id_foreign` FOREIGN KEY (`transfer_stop_id`) REFERENCES `transfer_stops` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_transportation_prices`
--

DROP TABLE IF EXISTS `activity_transportation_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_transportation_prices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) unsigned NOT NULL,
  `up_to_pax` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activity_transportation_prices_activity_id_foreign` (`activity_id`),
  CONSTRAINT `activity_transportation_prices_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10816 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_user`
--

DROP TABLE IF EXISTS `activity_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_user` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` int(10) unsigned NOT NULL,
  `user_id` int(10) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `addresses`
--

DROP TABLE IF EXISTS `addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `addresses` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `contactable_type` varchar(255) NOT NULL,
  `contactable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `line_1` varchar(255) NOT NULL,
  `line_2` varchar(255) DEFAULT NULL,
  `line_3` varchar(255) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL,
  `city` varchar(255) NOT NULL,
  `postal_code` varchar(255) NOT NULL,
  `country` varchar(255) DEFAULT NULL,
  `label` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `addresses_contactable_type_contactable_id_index` (`contactable_type`,`contactable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=965 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `airports`
--

DROP TABLE IF EXISTS `airports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `airports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `location_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `airports_location_id_foreign` (`location_id`),
  CONSTRAINT `airports_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `budget_qualities`
--

DROP TABLE IF EXISTS `budget_qualities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `budget_qualities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `low` int(11) NOT NULL,
  `medium` int(11) NOT NULL,
  `high` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `capacity_costs`
--

DROP TABLE IF EXISTS `capacity_costs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `capacity_costs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `part_type` varchar(255) NOT NULL,
  `part_id` bigint(20) unsigned NOT NULL,
  `up_to_pax` int(11) NOT NULL,
  `cost` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `capacity_costs_part_type_part_id_index` (`part_type`,`part_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2177 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `casa_categories`
--

DROP TABLE IF EXISTS `casa_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `casa_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `casa_category_location`
--

DROP TABLE IF EXISTS `casa_category_location`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `casa_category_location` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `casa_category_id` bigint(20) unsigned NOT NULL,
  `location_id` bigint(20) unsigned NOT NULL,
  `proposal_description` text DEFAULT NULL,
  `video` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=196 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `casa_category_location_user`
--

DROP TABLE IF EXISTS `casa_category_location_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `casa_category_location_user` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `casa_category_location_id` int(10) unsigned NOT NULL,
  `user_id` int(10) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `client_properties`
--

DROP TABLE IF EXISTS `client_properties`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_properties` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `external_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `comments`
--

DROP TABLE IF EXISTS `comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `comments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `comment` text NOT NULL,
  `comment_type` enum('from_reservas','for_reservas','for_provider','regular','from_operations') DEFAULT 'regular',
  `commentable_type` varchar(255) NOT NULL,
  `commentable_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `comments_user_id_foreign` (`user_id`),
  KEY `comments_commentable_type_commentable_id_index` (`commentable_type`,`commentable_id`),
  CONSTRAINT `comments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35393 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `companies`
--

DROP TABLE IF EXISTS `companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `companies` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `primary_colour` varchar(255) NOT NULL,
  `secondary_colour` varchar(255) NOT NULL,
  `accent_colour` varchar(255) NOT NULL,
  `default_customer_locale_id` bigint(20) unsigned DEFAULT NULL,
  `customer_locales` longtext DEFAULT NULL,
  `default` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `casa_name` varchar(255) NOT NULL,
  `casa_name_swap` tinyint(1) NOT NULL,
  `casa_standard_name` varchar(255) NOT NULL,
  `casa_premium_name` varchar(255) NOT NULL,
  `casa_boutique_name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `companies_default_customer_locale_id_foreign` (`default_customer_locale_id`),
  CONSTRAINT `companies_default_customer_locale_id_foreign` FOREIGN KEY (`default_customer_locale_id`) REFERENCES `locales` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `contact_form_customer_information_fields`
--

DROP TABLE IF EXISTS `contact_form_customer_information_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contact_form_customer_information_fields` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `form_id` bigint(20) unsigned NOT NULL,
  `title` varchar(255) NOT NULL,
  `title_info` varchar(255) NOT NULL,
  `title_availability` varchar(255) NOT NULL,
  `confirmation` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`confirmation`)),
  `honorific_field` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`honorific_field`)),
  `availability_field` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`availability_field`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `contacts`
--

DROP TABLE IF EXISTS `contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contacts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `contactable_type` varchar(255) NOT NULL,
  `contactable_id` bigint(20) unsigned NOT NULL,
  `provider_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contacts_contactable_type_contactable_id_index` (`contactable_type`,`contactable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8425 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `countries`
--

DROP TABLE IF EXISTS `countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `countries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `iso` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `map` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currencies`
--

DROP TABLE IF EXISTS `currencies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `currencies` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `iso` varchar(10) NOT NULL,
  `symbol` varchar(255) DEFAULT NULL,
  `base` tinyint(1) NOT NULL DEFAULT 0,
  `primary` tinyint(1) NOT NULL DEFAULT 0,
  `secondary` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `currency_rates`
--

DROP TABLE IF EXISTS `currency_rates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `currency_rates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `currency_1_id` bigint(20) unsigned NOT NULL,
  `currency_2_id` bigint(20) unsigned NOT NULL,
  `rates` decimal(8,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `currency_rates_currency_1_id_foreign` (`currency_1_id`),
  KEY `currency_rates_currency_2_id_foreign` (`currency_2_id`),
  CONSTRAINT `currency_rates_currency_1_id_foreign` FOREIGN KEY (`currency_1_id`) REFERENCES `currencies` (`id`),
  CONSTRAINT `currency_rates_currency_2_id_foreign` FOREIGN KEY (`currency_2_id`) REFERENCES `currencies` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `crm_id` int(10) unsigned DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customers_pipedrive_id_index` (`crm_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25471 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `default_payment_on_sites`
--

DROP TABLE IF EXISTS `default_payment_on_sites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `default_payment_on_sites` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `starting_at` date NOT NULL,
  `ending_at` date NOT NULL,
  `cost` bigint(20) unsigned NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `driver_oldtimer`
--

DROP TABLE IF EXISTS `driver_oldtimer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `driver_oldtimer` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `oldtimer_id` bigint(20) unsigned NOT NULL,
  `transfer_driver_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `driver_oldtimer_oldtimer_id_foreign` (`oldtimer_id`),
  KEY `driver_oldtimer_transfer_driver_id_foreign` (`transfer_driver_id`),
  CONSTRAINT `driver_oldtimer_oldtimer_id_foreign` FOREIGN KEY (`oldtimer_id`) REFERENCES `oldtimers` (`id`),
  CONSTRAINT `driver_oldtimer_transfer_driver_id_foreign` FOREIGN KEY (`transfer_driver_id`) REFERENCES `transfer_drivers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dropoffs`
--

DROP TABLE IF EXISTS `dropoffs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dropoffs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` text NOT NULL,
  `description` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_addresses`
--

DROP TABLE IF EXISTS `email_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_addresses` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `contactable_type` varchar(255) NOT NULL,
  `contactable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `value` varchar(255) NOT NULL,
  `label` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `email_addresses_contactable_type_contactable_id_index` (`contactable_type`,`contactable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27545 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `email_templates`
--

DROP TABLE IF EXISTS `email_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `locale_id` varchar(255) DEFAULT NULL,
  `external_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_templates_name_locale_id_unique` (`name`,`locale_id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `featured_activities`
--

DROP TABLE IF EXISTS `featured_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `featured_activities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `day` int(10) unsigned NOT NULL,
  `activity_id` bigint(20) unsigned NOT NULL,
  `location_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `featured_activities_activity_id_foreign` (`activity_id`),
  KEY `featured_activities_location_id_foreign` (`location_id`),
  CONSTRAINT `featured_activities_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`),
  CONSTRAINT `featured_activities_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `flights`
--

DROP TABLE IF EXISTS `flights`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flights` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `category` enum('economy','premium','business','first_class') DEFAULT NULL,
  `price` int(11) DEFAULT NULL,
  `cost` int(11) DEFAULT NULL,
  `scoring` int(10) unsigned DEFAULT NULL,
  `margin_percentage` double(8,2) DEFAULT NULL,
  `margin_absolute` double(8,2) DEFAULT NULL,
  `price_person` int(11) DEFAULT NULL,
  `rail_fly` int(10) unsigned DEFAULT NULL,
  `departure_airport_id` bigint(20) unsigned DEFAULT NULL,
  `departure_date` timestamp NULL DEFAULT NULL,
  `arrival_airport_id` bigint(20) unsigned DEFAULT NULL,
  `arrival_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `type` enum('as_agent','pauschalreise','private','no_flight') DEFAULT NULL,
  `status` enum('pending','booked','confirmed') DEFAULT NULL,
  `reviewed` tinyint(1) NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `flights_departure_airport_id_foreign` (`departure_airport_id`),
  KEY `flights_arrival_airport_id_foreign` (`arrival_airport_id`),
  CONSTRAINT `flights_arrival_airport_id_foreign` FOREIGN KEY (`arrival_airport_id`) REFERENCES `airports` (`id`),
  CONSTRAINT `flights_departure_airport_id_foreign` FOREIGN KEY (`departure_airport_id`) REFERENCES `airports` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73235 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `form_fields`
--

DROP TABLE IF EXISTS `form_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `form_fields` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `form_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  `required` tinyint(1) NOT NULL DEFAULT 0,
  `editable` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=184 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `forms`
--

DROP TABLE IF EXISTS `forms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `forms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `general_settings`
--

DROP TABLE IF EXISTS `general_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `general_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activities_margin` decimal(8,2) DEFAULT NULL,
  `transport_stops_aditional_margin` decimal(8,2) DEFAULT NULL,
  `transport_margin_colectivo` decimal(8,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_trip_bookings`
--

DROP TABLE IF EXISTS `group_trip_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_trip_bookings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` bigint(20) unsigned DEFAULT NULL,
  `order_id` bigint(20) unsigned DEFAULT NULL,
  `group_trip_id` bigint(20) unsigned NOT NULL,
  `group_trip_variant_id` bigint(20) unsigned NOT NULL,
  `room_allocation` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`room_allocation`)),
  `extras` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`extras`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_trip_extras`
--

DROP TABLE IF EXISTS `group_trip_extras`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_trip_extras` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_trip_id` bigint(20) unsigned NOT NULL,
  `sku` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `price` int(11) NOT NULL,
  `is_per_person` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_trip_extras_group_trip_id_foreign` (`group_trip_id`),
  CONSTRAINT `group_trip_extras_group_trip_id_foreign` FOREIGN KEY (`group_trip_id`) REFERENCES `group_trips` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_trip_variants`
--

DROP TABLE IF EXISTS `group_trip_variants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_trip_variants` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_trip_id` bigint(20) unsigned NOT NULL,
  `sku` varchar(255) NOT NULL,
  `date_start` date NOT NULL,
  `date_end` date NOT NULL,
  `capacity` smallint(6) NOT NULL,
  `price_single` mediumint(9) NOT NULL,
  `price_double` mediumint(9) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_trip_variants_group_trip_id_foreign` (`group_trip_id`),
  CONSTRAINT `group_trip_variants_group_trip_id_foreign` FOREIGN KEY (`group_trip_id`) REFERENCES `group_trips` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_trips`
--

DROP TABLE IF EXISTS `group_trips`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_trips` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `sku` varchar(255) NOT NULL,
  `count_cities` smallint(6) NOT NULL,
  `count_days` smallint(6) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_trips_sku_unique` (`sku`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `invoice_articles`
--

DROP TABLE IF EXISTS `invoice_articles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_articles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `external_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `invoice_templates`
--

DROP TABLE IF EXISTS `invoice_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `locale_id` varchar(255) DEFAULT NULL,
  `external_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_templates_name_locale_id_unique` (`name`,`locale_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `provider_id` int(10) unsigned NOT NULL,
  `external_id` int(10) unsigned NOT NULL,
  `external_url` varchar(255) DEFAULT NULL,
  `total` int(10) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB AUTO_INCREMENT=1222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `languages`
--

DROP TABLE IF EXISTS `languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `languages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `dialect` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `layovers`
--

DROP TABLE IF EXISTS `layovers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `layovers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `flight_id` bigint(20) unsigned NOT NULL,
  `flight` varchar(255) NOT NULL,
  `departure_time` time DEFAULT NULL,
  `departure_airport_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `layovers_flight_id_foreign` (`flight_id`),
  KEY `layovers_departure_airport_id_foreign` (`departure_airport_id`),
  CONSTRAINT `layovers_departure_airport_id_foreign` FOREIGN KEY (`departure_airport_id`) REFERENCES `airports` (`id`),
  CONSTRAINT `layovers_flight_id_foreign` FOREIGN KEY (`flight_id`) REFERENCES `flights` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28855 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lead_qualities`
--

DROP TABLE IF EXISTS `lead_qualities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `lead_qualities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `quality` varchar(255) NOT NULL,
  `min_revenue` int(11) DEFAULT NULL,
  `min_probability` int(11) DEFAULT NULL,
  `calendly_link` varchar(255) DEFAULT NULL,
  `beeqwil_calendly_link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `locale_trip`
--

DROP TABLE IF EXISTS `locale_trip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `locale_trip` (
  `trip_id` bigint(20) unsigned NOT NULL,
  `locale_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`trip_id`,`locale_id`),
  KEY `locale_trip_locale_id_foreign` (`locale_id`),
  CONSTRAINT `locale_trip_locale_id_foreign` FOREIGN KEY (`locale_id`) REFERENCES `locales` (`id`) ON DELETE CASCADE,
  CONSTRAINT `locale_trip_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `locales`
--

DROP TABLE IF EXISTS `locales`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `locales` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `currency_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `default` tinyint(1) NOT NULL DEFAULT 0,
  `default_user_id` bigint(20) unsigned DEFAULT NULL,
  `default_ai_assistant_id` varchar(255) DEFAULT NULL,
  `priority_countries` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`priority_countries`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `location_suggested_locations`
--

DROP TABLE IF EXISTS `location_suggested_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `location_suggested_locations` (
  `location_id` bigint(20) unsigned NOT NULL,
  `suggested_location_id` bigint(20) unsigned NOT NULL,
  KEY `location_suggested_locations_location_id_foreign` (`location_id`),
  KEY `location_suggested_locations_suggested_location_id_foreign` (`suggested_location_id`),
  CONSTRAINT `location_suggested_locations_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `location_suggested_locations_suggested_location_id_foreign` FOREIGN KEY (`suggested_location_id`) REFERENCES `locations` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `locations`
--

DROP TABLE IF EXISTS `locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `locations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `default_nights` int(10) unsigned DEFAULT 0,
  `region_id` bigint(20) unsigned NOT NULL,
  `country_id` bigint(20) unsigned NOT NULL,
  `has_beach` tinyint(1) NOT NULL DEFAULT 0,
  `just_activity` tinyint(1) NOT NULL DEFAULT 0,
  `kilometers` decimal(8,2) NOT NULL,
  `longitude` decimal(11,8) NOT NULL DEFAULT 0.00000000,
  `latitude` decimal(11,8) NOT NULL DEFAULT 0.00000000,
  `proposal_name` text DEFAULT NULL,
  `proposal_title` text DEFAULT NULL,
  `proposal_title_stop` text DEFAULT NULL,
  `sales_description` text DEFAULT NULL,
  `proposal_description` text DEFAULT NULL,
  `proposal_description_stop` text DEFAULT NULL,
  `video` varchar(255) DEFAULT NULL,
  `proposal_highlights` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '(DC2Type:json)' CHECK (json_valid(`proposal_highlights`)),
  `product_catalogue_description` text DEFAULT NULL,
  `tips` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tips`)),
  `is_featured` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `map_pin_x` varchar(255) DEFAULT NULL,
  `map_pin_y` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `locations_region_id_foreign` (`region_id`),
  KEY `locations_country_id_foreign` (`country_id`),
  CONSTRAINT `locations_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`),
  CONSTRAINT `locations_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `media`
--

DROP TABLE IF EXISTS `media`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `media` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  `uuid` char(36) DEFAULT NULL,
  `collection_name` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `mime_type` varchar(255) DEFAULT NULL,
  `disk` varchar(255) NOT NULL,
  `conversions_disk` varchar(255) DEFAULT NULL,
  `size` bigint(20) unsigned NOT NULL,
  `manipulations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`manipulations`)),
  `custom_properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`custom_properties`)),
  `generated_conversions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`generated_conversions`)),
  `responsive_images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`responsive_images`)),
  `order_column` int(10) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `media_uuid_unique` (`uuid`),
  KEY `media_model_type_model_id_index` (`model_type`,`model_id`),
  KEY `media_order_column_index` (`order_column`)
) ENGINE=InnoDB AUTO_INCREMENT=6946 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=424 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `model_has_permissions`
--

DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `model_has_roles`
--

DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) unsigned NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `oldtimer_language`
--

DROP TABLE IF EXISTS `oldtimer_language`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oldtimer_language` (
  `oldtimer_id` bigint(20) unsigned NOT NULL,
  `language_id` bigint(20) unsigned NOT NULL,
  KEY `oldtimer_language_oldtimer_id_foreign` (`oldtimer_id`),
  KEY `oldtimer_language_language_id_foreign` (`language_id`),
  CONSTRAINT `oldtimer_language_language_id_foreign` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`),
  CONSTRAINT `oldtimer_language_oldtimer_id_foreign` FOREIGN KEY (`oldtimer_id`) REFERENCES `oldtimers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `oldtimer_prices`
--

DROP TABLE IF EXISTS `oldtimer_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oldtimer_prices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `oldtimer_id` bigint(20) unsigned NOT NULL,
  `price` int(11) DEFAULT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `max_duration` int(11) DEFAULT NULL COMMENT 'value in minutes',
  `max_kilometers` int(11) DEFAULT NULL,
  `on_hours` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oldtimer_prices_oldtimer_id_foreign` (`oldtimer_id`),
  KEY `oldtimer_prices_currency_id_foreign` (`currency_id`),
  CONSTRAINT `oldtimer_prices_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`),
  CONSTRAINT `oldtimer_prices_oldtimer_id_foreign` FOREIGN KEY (`oldtimer_id`) REFERENCES `oldtimers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `oldtimer_settings`
--

DROP TABLE IF EXISTS `oldtimer_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oldtimer_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `pax` int(10) unsigned NOT NULL,
  `proposal_highlights` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '(DC2Type:json)' CHECK (json_valid(`proposal_highlights`)),
  `proposal_description` text DEFAULT NULL,
  `video` varchar(255) DEFAULT NULL,
  `dropoff_title` varchar(255) DEFAULT NULL,
  `dropoff_description` text DEFAULT NULL,
  `dropoff_title_airport` varchar(255) DEFAULT NULL,
  `dropoff_description_airport` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `proposal_title` text DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `oldtimers`
--

DROP TABLE IF EXISTS `oldtimers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oldtimers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `scoring_category` enum('A','B','C') DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `fuel_flatrate` int(11) DEFAULT NULL COMMENT 'value in EUR',
  `pricing_model` enum('daily','detailed') NOT NULL,
  `daily_fee` int(11) DEFAULT NULL,
  `first_km` int(11) DEFAULT NULL,
  `after_km` int(11) DEFAULT NULL,
  `return_fee` int(11) DEFAULT NULL,
  `first_day` int(11) DEFAULT NULL,
  `second_day` int(11) DEFAULT NULL,
  `third_day` int(11) DEFAULT NULL,
  `forth_day` int(11) DEFAULT NULL,
  `more_days` int(11) DEFAULT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `capacity` int(11) NOT NULL,
  `features` varchar(255) DEFAULT NULL,
  `make` varchar(255) DEFAULT NULL,
  `model` varchar(255) DEFAULT NULL,
  `year` int(10) unsigned DEFAULT NULL,
  `video` varchar(255) DEFAULT NULL,
  `max_luggage` int(11) DEFAULT NULL,
  `product_catalogue_description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oldtimers_currency_id_foreign` (`currency_id`),
  CONSTRAINT `oldtimers_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_customers`
--

DROP TABLE IF EXISTS `order_customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_customers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `iban` varchar(255) DEFAULT NULL,
  `salutation` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `street` varchar(255) NOT NULL,
  `street_number` varchar(255) NOT NULL,
  `street_additional` varchar(255) DEFAULT NULL,
  `city` varchar(255) NOT NULL,
  `zip` varchar(255) NOT NULL,
  `country_code` varchar(255) NOT NULL,
  `integration` varchar(255) DEFAULT NULL,
  `integration_id` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_customers_email_index` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=282 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL,
  `type` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `quantity` smallint(6) NOT NULL,
  `price` double NOT NULL,
  `due_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=528 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `order_travellers`
--

DROP TABLE IF EXISTS `order_travellers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_travellers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL,
  `driver` tinyint(1) NOT NULL DEFAULT 0,
  `full_info` tinyint(1) NOT NULL DEFAULT 0,
  `title` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `salutation` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `street` varchar(255) NOT NULL,
  `street_number` varchar(255) NOT NULL,
  `street_additional` varchar(255) DEFAULT NULL,
  `city` varchar(255) NOT NULL,
  `zip` varchar(255) NOT NULL,
  `country_code` varchar(255) NOT NULL,
  `birthdate` date NOT NULL,
  `nationality_code` varchar(255) NOT NULL,
  `passport_number` varchar(255) NOT NULL,
  `passport_expiry` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=689 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) unsigned NOT NULL,
  `order_number` varchar(255) DEFAULT NULL,
  `is_test` tinyint(1) NOT NULL DEFAULT 0,
  `status` varchar(255) NOT NULL,
  `sku` varchar(255) NOT NULL,
  `variation_sku` varchar(255) DEFAULT NULL,
  `productable_type` varchar(255) NOT NULL,
  `productable_id` bigint(20) unsigned NOT NULL,
  `variantable_type` varchar(255) DEFAULT NULL,
  `variantable_id` bigint(20) unsigned DEFAULT NULL,
  `gateway_method_id` bigint(20) unsigned NOT NULL,
  `summary` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `orders_order_number_unique` (`order_number`),
  KEY `orders_productable_type_productable_id_index` (`productable_type`,`productable_id`),
  KEY `orders_variantable_type_variantable_id_index` (`variantable_type`,`variantable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=286 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `organisations`
--

DROP TABLE IF EXISTS `organisations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `organisations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payment_details`
--

DROP TABLE IF EXISTS `payment_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_details` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) DEFAULT NULL,
  `invoice_type` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `bank_name` varchar(255) DEFAULT NULL,
  `transfer_type` varchar(255) DEFAULT NULL,
  `iban` text DEFAULT NULL,
  `bic` text DEFAULT NULL,
  `account_number` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `bank_address` text DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=417 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL,
  `payment_type` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `paid_on` timestamp NULL DEFAULT NULL,
  `amount` decimal(8,2) NOT NULL,
  `due_date` date NOT NULL,
  `gateway_method_id` bigint(20) unsigned DEFAULT NULL,
  `gateway_order` varchar(255) DEFAULT NULL,
  `invoice_provider` varchar(255) DEFAULT NULL,
  `invoice_provider_id` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=686 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `personal_access_tokens`
--

DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `phone_numbers`
--

DROP TABLE IF EXISTS `phone_numbers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `phone_numbers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `contactable_type` varchar(255) NOT NULL,
  `contactable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `value` varchar(255) NOT NULL,
  `country_code` varchar(255) DEFAULT NULL,
  `label` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `phone_numbers_contactable_type_contactable_id_index` (`contactable_type`,`contactable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25671 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `promotions`
--

DROP TABLE IF EXISTS `promotions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `promotions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `reservation_start_date` date NOT NULL,
  `reservation_end_date` date NOT NULL,
  `booking_start_date` date NOT NULL,
  `booking_end_date` date NOT NULL,
  `discount` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=275 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prompts`
--

DROP TABLE IF EXISTS `prompts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `prompts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `internal_tag` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `system_prompt` text NOT NULL,
  `user_prompt` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_settings`
--

DROP TABLE IF EXISTS `proposal_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `proposal_settings` (
  `title` text DEFAULT NULL,
  `subtitle` text DEFAULT NULL,
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) unsigned NOT NULL,
  `logos_title` text DEFAULT NULL,
  `usps_title` text DEFAULT NULL,
  `usps_list` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`usps_list`)),
  `messaging_title` text DEFAULT NULL,
  `messaging_tonnes_saved` int(11) DEFAULT NULL,
  `messaging_trees_planted` int(11) DEFAULT NULL,
  `messaging_emissions_flight` int(11) DEFAULT NULL,
  `messaging_emissions_transport` int(11) DEFAULT NULL,
  `messaging_emissions_accommodation` int(11) DEFAULT NULL,
  `messaging_note_title` text DEFAULT NULL,
  `messaging_note_description` text DEFAULT NULL,
  `exclusions_title` text DEFAULT NULL,
  `exclusions_subtitle` text DEFAULT NULL,
  `exclusions_list` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`exclusions_list`)),
  `insurance_title` text DEFAULT NULL,
  `insurance_subtitle` text DEFAULT NULL,
  `insurance_description` text DEFAULT NULL,
  `insurance_list` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`insurance_list`)),
  `terms_general` text DEFAULT NULL,
  `faqs_title` text DEFAULT NULL,
  `faqs_list` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`faqs_list`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `imprint` text DEFAULT NULL,
  `trustpilot_template_id` varchar(255) DEFAULT NULL,
  `trustpilot_business_id` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `opening_hours` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`opening_hours`)),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposals`
--

DROP TABLE IF EXISTS `proposals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `proposals` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `url` varchar(255) DEFAULT NULL,
  `status` enum('draft','published','error') NOT NULL DEFAULT 'draft',
  `last_error` text DEFAULT NULL,
  `version` int(10) unsigned NOT NULL DEFAULT 1,
  `trip_id` bigint(20) unsigned NOT NULL,
  `wordpress_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `proposals_trip_id_foreign` (`trip_id`),
  CONSTRAINT `proposals_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6726 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `providers`
--

DROP TABLE IF EXISTS `providers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `providers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `provider_number` varchar(20) DEFAULT NULL,
  `external_account_id` varchar(255) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `is_company` tinyint(1) NOT NULL DEFAULT 0,
  `organisation_id` bigint(20) unsigned DEFAULT NULL,
  `payment_detail_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `providers_provider_number_unique` (`provider_number`),
  KEY `providers_organisation_id_index` (`organisation_id`),
  KEY `providers_provider_number_index` (`provider_number`)
) ENGINE=InnoDB AUTO_INCREMENT=737 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pulse_aggregates`
--

DROP TABLE IF EXISTS `pulse_aggregates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pulse_aggregates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `bucket` int(10) unsigned NOT NULL,
  `period` mediumint(8) unsigned NOT NULL,
  `type` varchar(255) NOT NULL,
  `key` mediumtext NOT NULL,
  `key_hash` binary(16) GENERATED ALWAYS AS (unhex(md5(`key`))) VIRTUAL,
  `aggregate` varchar(255) NOT NULL,
  `value` decimal(20,2) NOT NULL,
  `count` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pulse_aggregates_bucket_period_type_aggregate_key_hash_unique` (`bucket`,`period`,`type`,`aggregate`,`key_hash`),
  KEY `pulse_aggregates_period_bucket_index` (`period`,`bucket`),
  KEY `pulse_aggregates_type_index` (`type`),
  KEY `pulse_aggregates_period_type_aggregate_bucket_index` (`period`,`type`,`aggregate`,`bucket`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pulse_entries`
--

DROP TABLE IF EXISTS `pulse_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pulse_entries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `timestamp` int(10) unsigned NOT NULL,
  `type` varchar(255) NOT NULL,
  `key` mediumtext NOT NULL,
  `key_hash` binary(16) GENERATED ALWAYS AS (unhex(md5(`key`))) VIRTUAL,
  `value` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pulse_entries_timestamp_index` (`timestamp`),
  KEY `pulse_entries_type_index` (`type`),
  KEY `pulse_entries_key_hash_index` (`key_hash`),
  KEY `pulse_entries_timestamp_type_key_hash_value_index` (`timestamp`,`type`,`key_hash`,`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pulse_values`
--

DROP TABLE IF EXISTS `pulse_values`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pulse_values` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `timestamp` int(10) unsigned NOT NULL,
  `type` varchar(255) NOT NULL,
  `key` mediumtext NOT NULL,
  `key_hash` binary(16) GENERATED ALWAYS AS (unhex(md5(`key`))) VIRTUAL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pulse_values_type_key_hash_unique` (`type`,`key_hash`),
  KEY `pulse_values_timestamp_index` (`timestamp`),
  KEY `pulse_values_type_index` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `question_logic`
--

DROP TABLE IF EXISTS `question_logic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `question_logic` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `related_question_id` bigint(20) unsigned NOT NULL,
  `question_id` bigint(20) unsigned NOT NULL,
  `operator` varchar(255) NOT NULL,
  `answer` varchar(255) DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `questions`
--

DROP TABLE IF EXISTS `questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `questions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `form_id` bigint(20) unsigned NOT NULL,
  `title` varchar(255) NOT NULL,
  `sub_title` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `image_description` text DEFAULT NULL,
  `category` varchar(255) DEFAULT NULL,
  `form_field_id` varchar(255) DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`data`)),
  `order_column` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `regions`
--

DROP TABLE IF EXISTS `regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `regions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `country_id` bigint(20) unsigned NOT NULL,
  `last_arrival_summer` time DEFAULT NULL,
  `last_arrival_winter` time DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `regions_country_id_foreign` (`country_id`),
  CONSTRAINT `regions_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rental_car_parts`
--

DROP TABLE IF EXISTS `rental_car_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rental_car_parts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `rental_car_id` bigint(20) unsigned NOT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `cost_model` varchar(255) NOT NULL,
  `cost` int(10) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rental_car_prices`
--

DROP TABLE IF EXISTS `rental_car_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rental_car_prices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `price` int(11) NOT NULL,
  `date_start` date NOT NULL,
  `date_end` date NOT NULL,
  `rental_car_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `rental_car_prices_rental_car_id_foreign` (`rental_car_id`),
  CONSTRAINT `rental_car_prices_rental_car_id_foreign` FOREIGN KEY (`rental_car_id`) REFERENCES `rental_cars` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rental_car_return_fees`
--

DROP TABLE IF EXISTS `rental_car_return_fees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rental_car_return_fees` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fee` int(11) DEFAULT NULL,
  `rental_car_id` bigint(20) unsigned NOT NULL,
  `pick_up_location_id` bigint(20) unsigned NOT NULL,
  `drop_off_location_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `rental_car_return_fees_rental_car_id_foreign` (`rental_car_id`),
  KEY `rental_car_return_fees_pick_up_location_id_foreign` (`pick_up_location_id`),
  KEY `rental_car_return_fees_drop_off_location_id_foreign` (`drop_off_location_id`),
  CONSTRAINT `rental_car_return_fees_drop_off_location_id_foreign` FOREIGN KEY (`drop_off_location_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `rental_car_return_fees_pick_up_location_id_foreign` FOREIGN KEY (`pick_up_location_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `rental_car_return_fees_rental_car_id_foreign` FOREIGN KEY (`rental_car_id`) REFERENCES `rental_cars` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3032 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rental_cars`
--

DROP TABLE IF EXISTS `rental_cars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rental_cars` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `scoring_category` enum('A','B','C') DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `pax` int(11) NOT NULL,
  `insurance` int(11) DEFAULT NULL,
  `assistance` int(11) DEFAULT NULL,
  `price` int(11) DEFAULT NULL,
  `price_peak` int(11) DEFAULT NULL,
  `gas_fee` int(10) unsigned DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `features` varchar(255) DEFAULT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `provider_id` bigint(20) unsigned DEFAULT NULL,
  `description` text DEFAULT NULL,
  `proposal_name` varchar(255) DEFAULT NULL,
  `proposal_description` text DEFAULT NULL,
  `dropoff_title` varchar(255) DEFAULT NULL,
  `dropoff_description` text DEFAULT NULL,
  `dropoff_title_airport` varchar(255) DEFAULT NULL,
  `dropoff_description_airport` text DEFAULT NULL,
  `category` varchar(255) DEFAULT NULL,
  `max_passengers` int(10) unsigned DEFAULT NULL,
  `max_luggage` int(10) unsigned DEFAULT NULL,
  `transmission_type` varchar(255) DEFAULT NULL,
  `make` varchar(255) DEFAULT NULL,
  `model` varchar(255) DEFAULT NULL,
  `product_catalogue_description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `travel_document_description` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `rental_cars_currency_id_foreign` (`currency_id`),
  CONSTRAINT `rental_cars_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `request_templates`
--

DROP TABLE IF EXISTS `request_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `request_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `role_has_permissions`
--

DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `role_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `display_name` varchar(255) DEFAULT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `display_name` varchar(255) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=251 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shop_payment_methods`
--

DROP TABLE IF EXISTS `shop_payment_methods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shop_payment_methods` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`title_json`)),
  `name` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `text` varchar(255) NOT NULL,
  `text_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`text_json`)),
  `locale_id` bigint(20) unsigned DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL,
  `default` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `shop_payment_methods_locale_id_foreign` (`locale_id`),
  CONSTRAINT `shop_payment_methods_locale_id_foreign` FOREIGN KEY (`locale_id`) REFERENCES `locales` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shop_settings`
--

DROP TABLE IF EXISTS `shop_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shop_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `key` varchar(255) NOT NULL,
  `value` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `shop_settings_key_unique` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shop_terms`
--

DROP TABLE IF EXISTS `shop_terms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shop_terms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `text` text NOT NULL,
  `applies_to` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`applies_to`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shop_texts`
--

DROP TABLE IF EXISTS `shop_texts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shop_texts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `text` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `short_notices`
--

DROP TABLE IF EXISTS `short_notices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `short_notices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `amount` int(10) unsigned NOT NULL,
  `days` int(10) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `submissions`
--

DROP TABLE IF EXISTS `submissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `submissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `is_test` tinyint(1) NOT NULL DEFAULT 0,
  `trip_id` bigint(20) unsigned DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `availability` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '(DC2Type:json)' CHECK (json_valid(`availability`)),
  `message` text NOT NULL,
  `answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`answers`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `quality_lead` varchar(255) DEFAULT NULL,
  `quality_budget` varchar(255) DEFAULT NULL,
  `score_revenue` int(11) DEFAULT NULL,
  `score_probability` int(11) DEFAULT NULL,
  `marketing_parameters` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`marketing_parameters`)),
  `locale_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5314 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `taggables`
--

DROP TABLE IF EXISTS `taggables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taggables` (
  `tag_id` bigint(20) unsigned NOT NULL,
  `taggable_type` varchar(255) NOT NULL,
  `taggable_id` bigint(20) unsigned NOT NULL,
  UNIQUE KEY `taggables_tag_id_taggable_id_taggable_type_unique` (`tag_id`,`taggable_id`,`taggable_type`),
  KEY `taggables_taggable_type_taggable_id_index` (`taggable_type`,`taggable_id`),
  CONSTRAINT `taggables_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tags`
--

DROP TABLE IF EXISTS `tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tags` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`name`)),
  `slug` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`slug`)),
  `type` varchar(255) DEFAULT NULL,
  `order_column` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=229 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `telescope_entries`
--

DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `batch_id` char(36) NOT NULL,
  `family_hash` varchar(255) DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `telescope_entries_tags`
--

DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) NOT NULL,
  `tag` varchar(255) NOT NULL,
  KEY `telescope_entries_tags_entry_uuid_tag_index` (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `telescope_monitoring`
--

DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `temporary_uploads`
--

DROP TABLE IF EXISTS `temporary_uploads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `temporary_uploads` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `session_id` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2918 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transfer_driver_details`
--

DROP TABLE IF EXISTS `transfer_driver_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transfer_driver_details` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transfer_id` bigint(20) unsigned NOT NULL,
  `transfer_driver_id` bigint(20) unsigned NOT NULL,
  `pax` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transfer_driver_details_transfer_id_foreign` (`transfer_id`),
  KEY `transfer_driver_details_transfer_driver_id_foreign` (`transfer_driver_id`),
  KEY `transfer_driver_details_currency_id_foreign` (`currency_id`),
  CONSTRAINT `transfer_driver_details_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`),
  CONSTRAINT `transfer_driver_details_transfer_driver_id_foreign` FOREIGN KEY (`transfer_driver_id`) REFERENCES `transfer_drivers` (`id`),
  CONSTRAINT `transfer_driver_details_transfer_id_foreign` FOREIGN KEY (`transfer_id`) REFERENCES `transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33147 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transfer_driver_hourly_rates`
--

DROP TABLE IF EXISTS `transfer_driver_hourly_rates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transfer_driver_hourly_rates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transfer_driver_id` bigint(20) unsigned NOT NULL,
  `pax` int(11) NOT NULL,
  `duration` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transfer_driver_hourly_rates_transfer_driver_id_foreign` (`transfer_driver_id`),
  CONSTRAINT `transfer_driver_hourly_rates_transfer_driver_id_foreign` FOREIGN KEY (`transfer_driver_id`) REFERENCES `transfer_drivers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3421 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transfer_driver_language`
--

DROP TABLE IF EXISTS `transfer_driver_language`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transfer_driver_language` (
  `transfer_driver_id` bigint(20) unsigned NOT NULL,
  `language_id` bigint(20) unsigned NOT NULL,
  KEY `transfer_driver_language_transfer_driver_id_foreign` (`transfer_driver_id`),
  KEY `transfer_driver_language_language_id_foreign` (`language_id`),
  CONSTRAINT `transfer_driver_language_language_id_foreign` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`),
  CONSTRAINT `transfer_driver_language_transfer_driver_id_foreign` FOREIGN KEY (`transfer_driver_id`) REFERENCES `transfer_drivers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transfer_drivers`
--

DROP TABLE IF EXISTS `transfer_drivers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transfer_drivers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `type` enum('oldtimer','transfer') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transfer_drivers_currency_id_foreign` (`currency_id`),
  CONSTRAINT `transfer_drivers_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transfer_settings`
--

DROP TABLE IF EXISTS `transfer_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transfer_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `bus_title` varchar(255) DEFAULT NULL,
  `bus_description` text DEFAULT NULL,
  `group_title` varchar(255) DEFAULT NULL,
  `group_description` text DEFAULT NULL,
  `private_title` varchar(255) DEFAULT NULL,
  `private_description` text DEFAULT NULL,
  `origin` varchar(255) DEFAULT NULL,
  `destination` varchar(255) DEFAULT NULL,
  `duration` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transfer_stops`
--

DROP TABLE IF EXISTS `transfer_stops`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transfer_stops` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transfer_id` bigint(20) unsigned NOT NULL,
  `location_id` bigint(20) unsigned NOT NULL,
  `duration` int(11) NOT NULL,
  `order` int(11) NOT NULL,
  `max_activity_limitation` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transfer_stops_transfer_id_foreign` (`transfer_id`),
  KEY `transfer_stops_location_id_foreign` (`location_id`),
  CONSTRAINT `transfer_stops_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `transfer_stops_transfer_id_foreign` FOREIGN KEY (`transfer_id`) REFERENCES `transfers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `transfers`
--

DROP TABLE IF EXISTS `transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transfers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `scoring_category` enum('A','B','C') DEFAULT NULL,
  `location_origin_id` bigint(20) unsigned NOT NULL,
  `location_destination_id` bigint(20) unsigned NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `distance` int(11) DEFAULT NULL,
  `type` enum('private','collectivo','domestic_flight') NOT NULL DEFAULT 'private',
  `features` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transfers_location_origin_id_foreign` (`location_origin_id`),
  KEY `transfers_location_destination_id_foreign` (`location_destination_id`),
  CONSTRAINT `transfers_location_destination_id_foreign` FOREIGN KEY (`location_destination_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `transfers_location_origin_id_foreign` FOREIGN KEY (`location_origin_id`) REFERENCES `locations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=785 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_accommodation_rooms`
--

DROP TABLE IF EXISTS `trip_accommodation_rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_accommodation_rooms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `price_type` varchar(255) DEFAULT NULL,
  `placed_travelers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`placed_travelers`)),
  `trip_city_id` bigint(20) unsigned NOT NULL,
  `accommodation_id` bigint(20) unsigned NOT NULL,
  `accommodation_room_id` bigint(20) unsigned NOT NULL,
  `room_type` enum('single','double','triple','quadruple') DEFAULT NULL,
  `number_of_rooms` int(10) unsigned NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trip_accommodation_rooms_trip_city_id_foreign` (`trip_city_id`),
  KEY `trip_accommodation_rooms_accommodation_id_foreign` (`accommodation_id`),
  KEY `trip_accommodation_rooms_accommodation_room_id_foreign` (`accommodation_room_id`),
  CONSTRAINT `trip_accommodation_rooms_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`),
  CONSTRAINT `trip_accommodation_rooms_accommodation_room_id_foreign` FOREIGN KEY (`accommodation_room_id`) REFERENCES `accommodation_rooms` (`id`),
  CONSTRAINT `trip_accommodation_rooms_trip_city_id_foreign` FOREIGN KEY (`trip_city_id`) REFERENCES `trip_cities` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=850103 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_activities`
--

DROP TABLE IF EXISTS `trip_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_activities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` bigint(20) unsigned NOT NULL,
  `activity_id` bigint(20) unsigned NOT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `adults` int(10) unsigned DEFAULT NULL,
  `children` int(10) unsigned DEFAULT NULL,
  `infants` int(10) unsigned DEFAULT NULL,
  `price` int(11) DEFAULT NULL,
  `adjust_cost` int(11) DEFAULT NULL,
  `adjust_cost_type` enum('Reduction','Addition') DEFAULT NULL,
  `adjust_cost_reason` enum('Agency','Partner') DEFAULT NULL,
  `adjust_cost_by_user_id` bigint(20) unsigned DEFAULT NULL,
  `adjust_cost_at` timestamp NULL DEFAULT NULL,
  `cost` int(11) DEFAULT NULL,
  `scoring` int(10) unsigned DEFAULT NULL,
  `booking_status` enum('not_requested','confirmed','requested','cancelled') DEFAULT 'not_requested',
  `requested_at` timestamp NULL DEFAULT NULL,
  `cancellation_related_id` bigint(20) unsigned DEFAULT NULL,
  `cancellation_reason` varchar(255) DEFAULT NULL,
  `provider_id` bigint(20) unsigned DEFAULT NULL,
  `reviewed` tinyint(1) NOT NULL DEFAULT 0,
  `confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `comment` varchar(255) DEFAULT NULL,
  `external_booking_number` varchar(255) DEFAULT NULL,
  `internal_invoice_number` varchar(255) DEFAULT NULL,
  `payment_status` enum('open','needs_review','processing','credited','processed','paid') DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `invoice_id` bigint(20) unsigned DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trip_activities_activity_id_foreign` (`activity_id`),
  KEY `trip_activities_trip_id_booking_status_index` (`trip_id`,`booking_status`),
  KEY `trip_activities_provider_id_index` (`provider_id`),
  KEY `trip_activities_adjust_cost_by_user_id_foreign` (`adjust_cost_by_user_id`),
  CONSTRAINT `trip_activities_activity_id_foreign` FOREIGN KEY (`activity_id`) REFERENCES `activities` (`id`),
  CONSTRAINT `trip_activities_adjust_cost_by_user_id_foreign` FOREIGN KEY (`adjust_cost_by_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `trip_activities_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=618374 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_additional_services`
--

DROP TABLE IF EXISTS `trip_additional_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_additional_services` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `reviewed` tinyint(1) NOT NULL DEFAULT 0,
  `confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `booking_status` enum('not_requested','confirmed','requested','cancelled') DEFAULT 'not_requested',
  `provider_id` bigint(20) unsigned DEFAULT NULL,
  `requested_at` timestamp NULL DEFAULT NULL,
  `cancellation_related_id` bigint(20) unsigned DEFAULT NULL,
  `cancellation_reason` varchar(255) DEFAULT NULL,
  `payment_status` enum('open','needs_review','processing','credited','processed','paid') DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `invoice_id` bigint(20) unsigned DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `date` date DEFAULT NULL,
  `amount` int(10) unsigned DEFAULT 1,
  `type` varchar(255) DEFAULT NULL,
  `trip_id` bigint(20) unsigned NOT NULL,
  `price_item` int(10) unsigned NOT NULL,
  `cost` int(10) unsigned DEFAULT NULL,
  `margin_percentage` double(8,2) DEFAULT NULL,
  `margin_absolute` double(8,2) DEFAULT NULL,
  `price_sale` int(10) unsigned NOT NULL,
  `price` int(10) unsigned DEFAULT NULL,
  `adjust_cost` int(11) DEFAULT NULL,
  `adjust_cost_type` enum('Reduction','Addition') DEFAULT NULL,
  `adjust_cost_reason` enum('Agency','Partner') DEFAULT NULL,
  `adjust_cost_by_user_id` bigint(20) unsigned DEFAULT NULL,
  `adjust_cost_at` timestamp NULL DEFAULT NULL,
  `currency_id` bigint(20) unsigned NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trip_additional_services_trip_id_foreign` (`trip_id`),
  KEY `trip_additional_services_currency_id_foreign` (`currency_id`),
  KEY `trip_additional_services_adjust_cost_by_user_id_foreign` (`adjust_cost_by_user_id`),
  KEY `trip_additional_services_provider_id_index` (`provider_id`),
  CONSTRAINT `trip_additional_services_adjust_cost_by_user_id_foreign` FOREIGN KEY (`adjust_cost_by_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `trip_additional_services_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`),
  CONSTRAINT `trip_additional_services_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6565 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_cities`
--

DROP TABLE IF EXISTS `trip_cities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_cities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `is_stop` tinyint(1) DEFAULT 0,
  `stops_duration` int(11) DEFAULT NULL,
  `trip_transport_id` bigint(20) unsigned DEFAULT NULL,
  `trip_id` bigint(20) unsigned NOT NULL,
  `location_id` bigint(20) unsigned NOT NULL,
  `number_of_days` int(10) unsigned NOT NULL,
  `accommodation_id` bigint(20) unsigned DEFAULT NULL,
  `check_in` time DEFAULT NULL,
  `check_out` time DEFAULT NULL,
  `booking_start` date NOT NULL,
  `booking_end` date NOT NULL,
  `price` int(11) DEFAULT NULL,
  `adjust_cost` int(11) DEFAULT NULL,
  `adjust_cost_type` enum('Reduction','Addition') DEFAULT NULL,
  `adjust_cost_reason` enum('Agency','Partner') DEFAULT NULL,
  `adjust_cost_by_user_id` bigint(20) unsigned DEFAULT NULL,
  `adjust_cost_at` timestamp NULL DEFAULT NULL,
  `cost` int(11) DEFAULT NULL,
  `scoring` int(10) unsigned DEFAULT NULL,
  `margin_percentage` double(8,2) DEFAULT NULL,
  `margin_absolute` double(8,2) DEFAULT NULL,
  `booking_status` enum('not_requested','confirmed','requested','cancelled') DEFAULT 'not_requested',
  `requested_at` timestamp NULL DEFAULT NULL,
  `cancellation_related_id` bigint(20) unsigned DEFAULT NULL,
  `cancellation_reason` varchar(255) DEFAULT NULL,
  `provider_id` bigint(20) unsigned DEFAULT NULL,
  `reviewed` tinyint(1) NOT NULL DEFAULT 0,
  `confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `self_organized` tinyint(1) NOT NULL DEFAULT 0,
  `self_organized_address` text DEFAULT NULL,
  `external_booking_number` varchar(255) DEFAULT NULL,
  `internal_invoice_number` varchar(255) DEFAULT NULL,
  `payment_status` enum('open','needs_review','processing','credited','processed','paid') DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `invoice_id` bigint(20) unsigned DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trip_cities_trip_id_foreign` (`trip_id`),
  KEY `trip_cities_location_id_foreign` (`location_id`),
  KEY `trip_cities_accommodation_id_foreign` (`accommodation_id`),
  KEY `trip_cities_trip_id_booking_status_index` (`trip_id`,`booking_status`),
  KEY `trip_cities_trip_transport_id_foreign` (`trip_transport_id`),
  KEY `trip_cities_provider_id_index` (`provider_id`),
  KEY `trip_cities_adjust_cost_by_user_id_foreign` (`adjust_cost_by_user_id`),
  CONSTRAINT `trip_cities_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`),
  CONSTRAINT `trip_cities_adjust_cost_by_user_id_foreign` FOREIGN KEY (`adjust_cost_by_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `trip_cities_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `trip_cities_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`),
  CONSTRAINT `trip_cities_trip_transport_id_foreign` FOREIGN KEY (`trip_transport_id`) REFERENCES `trip_transports` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=686873 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_comments`
--

DROP TABLE IF EXISTS `trip_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_comments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `trip_version_id` bigint(20) unsigned DEFAULT NULL,
  `comment` text NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trip_comments_trip_id_index` (`trip_id`),
  KEY `trip_comments_user_id_index` (`user_id`),
  KEY `trip_comments_trip_version_id_index` (`trip_version_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_default_rooms`
--

DROP TABLE IF EXISTS `trip_default_rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_default_rooms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` bigint(20) unsigned NOT NULL,
  `placed_travelers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`placed_travelers`)),
  `accommodation_room_type_id` bigint(20) unsigned DEFAULT NULL,
  `room_type` enum('single','double','triple','quadruple') DEFAULT NULL,
  `number_rooms` int(10) unsigned NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trip_default_rooms_accommodation_room_type_id_foreign` (`accommodation_room_type_id`),
  KEY `trip_default_rooms_trip_id_foreign` (`trip_id`),
  CONSTRAINT `trip_default_rooms_accommodation_room_type_id_foreign` FOREIGN KEY (`accommodation_room_type_id`) REFERENCES `accommodation_room_types` (`id`),
  CONSTRAINT `trip_default_rooms_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=161503 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_language`
--

DROP TABLE IF EXISTS `trip_language`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_language` (
  `trip_id` bigint(20) unsigned NOT NULL,
  `language_id` bigint(20) unsigned NOT NULL,
  KEY `trip_language_trip_id_foreign` (`trip_id`),
  KEY `trip_language_language_id_foreign` (`language_id`),
  CONSTRAINT `trip_language_language_id_foreign` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`),
  CONSTRAINT `trip_language_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_parts`
--

DROP TABLE IF EXISTS `trip_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_parts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `trip_service_type` varchar(255) NOT NULL,
  `trip_service_id` bigint(20) unsigned NOT NULL,
  `part_type` varchar(255) NOT NULL,
  `part_id` bigint(20) unsigned NOT NULL,
  `provider_id` bigint(20) unsigned DEFAULT NULL,
  `booking_status` varchar(255) NOT NULL,
  `external_booking_number` varchar(255) DEFAULT NULL,
  `internal_invoice_number` varchar(255) DEFAULT NULL,
  `cost` int(11) DEFAULT NULL,
  `margin_percentage` double(8,2) DEFAULT NULL,
  `margin_absolute` double(8,2) DEFAULT NULL,
  `price` int(11) DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `requested_at` datetime DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `payment_status` enum('open','needs_review','processing','credited','processed','paid') NOT NULL DEFAULT 'open',
  `adjust_cost_at` timestamp NULL DEFAULT NULL,
  `adjust_cost_by_user_id` bigint(20) unsigned DEFAULT NULL,
  `adjust_cost_reason` enum('Agency','Partner') DEFAULT NULL,
  `adjust_cost_type` enum('Reduction','Addition') DEFAULT NULL,
  `adjust_cost` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_part_index` (`trip_service_id`,`trip_service_type`,`part_id`,`part_type`),
  KEY `trip_parts_trip_service_type_trip_service_id_index` (`trip_service_type`,`trip_service_id`),
  KEY `trip_parts_part_type_part_id_index` (`part_type`,`part_id`),
  KEY `trip_parts_adjust_cost_by_user_id_foreign` (`adjust_cost_by_user_id`),
  KEY `trip_service_type` (`trip_service_type`),
  CONSTRAINT `trip_parts_adjust_cost_by_user_id_foreign` FOREIGN KEY (`adjust_cost_by_user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6187244 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trip_transports`
--

DROP TABLE IF EXISTS `trip_transports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trip_transports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `trip_id` bigint(20) unsigned NOT NULL,
  `type` enum('airport_outbound','airport_return','oldtimer','rental_car','private') NOT NULL,
  `mode` enum('manual','auto') NOT NULL DEFAULT 'manual',
  `transfer_needed` tinyint(1) DEFAULT NULL,
  `location_destination_id` bigint(20) unsigned DEFAULT NULL,
  `location_origin_id` bigint(20) unsigned DEFAULT NULL,
  `transportable_id` bigint(20) unsigned DEFAULT NULL,
  `transportable_type` varchar(255) NOT NULL DEFAULT 'App\\Models\\Transfer',
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `pick_up_time` time DEFAULT NULL,
  `pick_up_address` varchar(255) DEFAULT NULL,
  `drop_off_time` time DEFAULT NULL,
  `drop_off_address` varchar(255) DEFAULT NULL,
  `price` int(11) DEFAULT NULL,
  `adjust_cost` int(11) DEFAULT NULL,
  `adjust_cost_type` enum('Reduction','Addition') DEFAULT NULL,
  `adjust_cost_reason` enum('Agency','Partner') DEFAULT NULL,
  `adjust_cost_by_user_id` bigint(20) unsigned DEFAULT NULL,
  `adjust_cost_at` timestamp NULL DEFAULT NULL,
  `cost` int(11) DEFAULT NULL,
  `scoring` int(10) unsigned DEFAULT NULL,
  `margin_percentage` double(8,2) DEFAULT NULL,
  `margin_absolute` double(8,2) DEFAULT NULL,
  `booking_status` enum('not_requested','confirmed','requested','cancelled') DEFAULT 'not_requested',
  `requested_at` timestamp NULL DEFAULT NULL,
  `cancellation_related_id` bigint(20) unsigned DEFAULT NULL,
  `cancellation_reason` varchar(255) DEFAULT NULL,
  `provider_id` bigint(20) unsigned DEFAULT NULL,
  `booking_number` varchar(255) DEFAULT NULL,
  `transfer_driver_id` bigint(20) unsigned DEFAULT NULL,
  `reviewed` tinyint(1) NOT NULL DEFAULT 0,
  `confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `self_organized` tinyint(1) NOT NULL DEFAULT 0,
  `external_booking_number` varchar(255) DEFAULT NULL,
  `internal_invoice_number` varchar(255) DEFAULT NULL,
  `payment_status` enum('open','needs_review','processing','credited','processed','paid') DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `invoice_id` bigint(20) unsigned DEFAULT NULL,
  `confirmed_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trip_transports_trip_id_foreign` (`trip_id`),
  KEY `trip_transports_location_origin_id_foreign` (`location_origin_id`),
  KEY `trip_transports_location_destination_id_foreign` (`location_destination_id`),
  KEY `trip_transports_transfer_driver_id_foreign` (`transfer_driver_id`),
  KEY `trip_transports_trip_id_booking_status_index` (`trip_id`,`booking_status`),
  KEY `trip_transports_provider_id_index` (`provider_id`),
  KEY `trip_transports_adjust_cost_by_user_id_foreign` (`adjust_cost_by_user_id`),
  CONSTRAINT `trip_transports_adjust_cost_by_user_id_foreign` FOREIGN KEY (`adjust_cost_by_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `trip_transports_location_destination_id_foreign` FOREIGN KEY (`location_destination_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `trip_transports_location_origin_id_foreign` FOREIGN KEY (`location_origin_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `trip_transports_transfer_driver_id_foreign` FOREIGN KEY (`transfer_driver_id`) REFERENCES `transfer_drivers` (`id`),
  CONSTRAINT `trip_transports_trip_id_foreign` FOREIGN KEY (`trip_id`) REFERENCES `trips` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=709108 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `trips`
--

DROP TABLE IF EXISTS `trips`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trips` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) unsigned DEFAULT NULL,
  `on_site_payment_status` enum('open','paid') DEFAULT 'open',
  `version` int(11) NOT NULL DEFAULT 1,
  `initial_trip_id` bigint(20) unsigned DEFAULT NULL,
  `customer_id` bigint(20) unsigned DEFAULT NULL,
  `is_automated` tinyint(1) NOT NULL DEFAULT 0,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta`)),
  `type` enum('trip','template','group') NOT NULL DEFAULT 'trip',
  `pipedrive_fields` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`pipedrive_fields`)),
  `crm_id` varchar(255) DEFAULT NULL,
  `deal_number` varchar(255) DEFAULT NULL,
  `inquiry_date` date DEFAULT NULL,
  `trip_name` varchar(255) DEFAULT NULL,
  `price` int(10) unsigned DEFAULT NULL,
  `cost` int(11) DEFAULT NULL,
  `scoring` int(10) unsigned DEFAULT NULL,
  `cost_sale` int(10) unsigned DEFAULT NULL,
  `discount` decimal(8,2) DEFAULT NULL,
  `express_processing` tinyint(1) NOT NULL DEFAULT 0,
  `travelers` int(10) unsigned DEFAULT NULL,
  `travelers_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`travelers_info`)),
  `trip_length` int(10) unsigned DEFAULT NULL,
  `status` enum('open','won','lost','unused','deleted','booked') DEFAULT 'open',
  `is_booker_version` tinyint(1) NOT NULL DEFAULT 0,
  `booker_version_of_trip_id` bigint(20) unsigned DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `won_at` timestamp NULL DEFAULT NULL,
  `lost_at` timestamp NULL DEFAULT NULL,
  `sales_buddy_id` bigint(20) unsigned DEFAULT NULL,
  `local_buddy_id` bigint(20) unsigned DEFAULT NULL,
  `booking_user_id` bigint(20) unsigned DEFAULT NULL,
  `payment_on_site` int(10) unsigned DEFAULT NULL,
  `number_travelers` int(10) unsigned DEFAULT NULL,
  `number_travelers_changed` tinyint(1) DEFAULT 0,
  `number_adults` int(10) unsigned DEFAULT NULL,
  `number_children` int(10) unsigned DEFAULT NULL,
  `number_infants` int(10) unsigned DEFAULT NULL,
  `express_processing_cost` int(11) DEFAULT NULL,
  `sales_score` int(11) DEFAULT NULL,
  `outbound_flight_id` bigint(20) unsigned DEFAULT NULL,
  `return_flight_id` bigint(20) unsigned DEFAULT NULL,
  `trip_start` date DEFAULT NULL,
  `trip_end` date DEFAULT NULL,
  `group_trip_booking_id` bigint(20) unsigned DEFAULT NULL,
  `language_id` bigint(20) unsigned DEFAULT NULL,
  `currency_id` bigint(20) unsigned DEFAULT NULL,
  `locale_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deal` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`deal`)),
  `deleted_at` timestamp NULL DEFAULT NULL,
  `proposal_message` text DEFAULT NULL,
  `proposal_email` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`proposal_email`)),
  `proposal_settings` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`proposal_settings`)),
  `offer_email_sent_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trips_sales_buddy_id_foreign` (`sales_buddy_id`),
  KEY `trips_local_buddy_id_foreign` (`local_buddy_id`),
  KEY `trips_outbound_flight_id_foreign` (`outbound_flight_id`),
  KEY `trips_return_flight_id_foreign` (`return_flight_id`),
  KEY `trips_status_index` (`status`),
  KEY `trips_initial_trip_id_foreign` (`initial_trip_id`),
  KEY `trips_customer_id_index` (`customer_id`),
  KEY `trips_booking_user_id_foreign` (`booking_user_id`),
  KEY `trips_template_id_foreign` (`template_id`),
  KEY `trips_is_booker_version_index` (`is_booker_version`),
  KEY `trips_crm_id_index` (`crm_id`),
  KEY `trips_deleted_at_index` (`deleted_at`),
  KEY `trips_crm_id_is_booker_version_index` (`crm_id`,`is_booker_version`),
  KEY `trips_locale_id_foreign` (`locale_id`),
  CONSTRAINT `trips_booking_user_id_foreign` FOREIGN KEY (`booking_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `trips_initial_trip_id_foreign` FOREIGN KEY (`initial_trip_id`) REFERENCES `trips` (`id`),
  CONSTRAINT `trips_local_buddy_id_foreign` FOREIGN KEY (`local_buddy_id`) REFERENCES `users` (`id`),
  CONSTRAINT `trips_locale_id_foreign` FOREIGN KEY (`locale_id`) REFERENCES `locales` (`id`),
  CONSTRAINT `trips_outbound_flight_id_foreign` FOREIGN KEY (`outbound_flight_id`) REFERENCES `flights` (`id`) ON DELETE CASCADE,
  CONSTRAINT `trips_return_flight_id_foreign` FOREIGN KEY (`return_flight_id`) REFERENCES `flights` (`id`) ON DELETE CASCADE,
  CONSTRAINT `trips_sales_buddy_id_foreign` FOREIGN KEY (`sales_buddy_id`) REFERENCES `users` (`id`),
  CONSTRAINT `trips_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `trips` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=52891 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) unsigned DEFAULT NULL,
  `preferred_local_buddy_id` bigint(20) unsigned DEFAULT NULL,
  `ai_assistant_id` varchar(255) DEFAULT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `job_title` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`job_title`)),
  `proposal_message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`proposal_message`)),
  `proposal_calendar_link` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`proposal_calendar_link`)),
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `new_password_required` tinyint(1) NOT NULL DEFAULT 0,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `phone` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`phone`)),
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `users_preferred_local_buddy_id_foreign` (`preferred_local_buddy_id`),
  CONSTRAINT `users_preferred_local_buddy_id_foreign` FOREIGN KEY (`preferred_local_buddy_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-05  9:08:47
