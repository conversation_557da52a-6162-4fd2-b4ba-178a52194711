""" This is with enhanced error debugging and multiple issues solver"""

"""
Billomat Cloud Function for automated data ingestion
Triggered by Cloud Scheduler to retrieve and store Billomat data in GCS
"""
import os
import logging
import traceback
from datetime import datetime, timedelta, date
import calendar
from typing import Optional, Dict, Any
from flask import Request, jsonify

# Setup custom logger first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_environment():
    """Setup environment variables if not in production"""
    # Log all environment variables for debugging
    logger.info("=== ENVIRONMENT VARIABLES AT STARTUP ===")
    for key, value in os.environ.items():
        if 'API_KEY' not in key and 'SECRET' not in key and 'PASSWORD' not in key:
            logger.info(f"{key}: {value}")
        else:
            logger.info(f"{key}: [REDACTED]")

    # In Cloud Functions, we'll use environment variables set during deployment
    if not os.getenv('GCP_PROJECT'):  # Check if running locally
        try:
            from dotenv import load_dotenv
            load_dotenv()
            logger.info("Loaded environment variables from .env file")
        except ImportError:
            logger.warning("dotenv module not found, skipping .env loading")

# Set up environment before imports to ensure variables are available
setup_environment()

# Now import other modules
try:
    #updated with src path
    from src.data_pipelines.billomat.client import BillomatClient
    from src.data_pipelines.billomat.processor import BillomatDataProcessor
    from src.infrastructure.gcs.storage_manager.billomat_storage import BillomatGCSStorage
    logger.info("Successfully imported all required modules")
except ImportError as e:
    logger.error(f"Error importing modules: {str(e)}")
    traceback.print_exc()


def get_date_range() -> tuple:
    """
    Calculate the date range to be used for data retrieval.
    Gets data from 7 days ago until the day the scheduler is triggered.

    Returns:
        tuple: (from_date, to_date) in YYYY-MM-DD format
    """
    to_date = datetime.now()
    from_date = to_date - timedelta(days=7)

    return from_date.strftime('%Y-%m-%d'), to_date.strftime('%Y-%m-%d')
    # to_date = '2025-05-02'
    # from_date= '2025-04-13'
    # return from_date, to_date

def billomat_http_trigger(request):
    """HTTP trigger wrapper for billomat_ingestion"""
    try:
        # Log the request for debugging
        logger.info(f"Received HTTP request: {request.method}")
        logger.info(f"Request headers: {dict(request.headers)}")

        # Debug: Print all environment variables again
        logger.info("=== ENVIRONMENT VARIABLES IN HTTP TRIGGER ===")
        for key, value in os.environ.items():
            if 'API_KEY' not in key and 'SECRET' not in key and 'PASSWORD' not in key:
                logger.info(f"{key}: {value}")
            else:
                logger.info(f"{key}: [REDACTED]")

        # Input validation
        if request.method != 'POST' and request.method != 'GET':
            return jsonify({"error": "Only POST and GET methods are supported"}), 405

        # Convert request to appropriate event format
        event = {}
        if request.method == 'POST':
            if request.is_json:
                event = request.get_json()
                logger.info(f"Received JSON data: {event}")
            else:
                event = {"data": request.data.decode('utf-8')}
                logger.info(f"Received non-JSON data: {event}")
        elif request.method == 'GET':
            event = dict(request.args)
            logger.info(f"Received GET parameters: {event}")

        # Call the main function with the event and None context
        result = billomat_ingestion(event, None)

        # Return proper HTTP response
        return jsonify({"success": True, "result": result}), 200

    except ValueError as ve:
        # Handle validation errors separately
        logger.error(f"Validation error: {str(ve)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(ve)}), 400

    except Exception as e:
        # General error handling
        logger.error(f"Error in HTTP trigger: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)}), 500


def billomat_ingestion(event, context=None):
    """
    Cloud Function entry point. Triggered by Cloud Scheduler.
    Retrieves Billomat data and stores it in GCS.

    Args:
        event (dict): Event payload
        context: Metadata for the event
    """
    try:
        # Debug: Log all environment variables at the beginning of function execution
        logger.info("=== ENVIRONMENT VARIABLES IN BILLOMAT_INGESTION ===")
        for key, value in os.environ.items():
            if 'API_KEY' not in key and 'SECRET' not in key and 'PASSWORD' not in key:
                logger.info(f"{key}: {value}")
            else:
                logger.info(f"{key}: [REDACTED]")

        # Log execution details
        logger.info(f"Billomat ingestion function started at {datetime.now().isoformat()}")
        logger.info(f"Event: {event}")

        # Check if API key is configured with detailed logging
        logger.info("Checking for BILLOMAT_API_KEY...")
        api_key = os.getenv('BILLOMAT_API_KEY')
        if not api_key:
            logger.error("BILLOMAT_API_KEY environment variable not set")
            raise ValueError("BILLOMAT_API_KEY environment variable not set")
        else:
            logger.info("BILLOMAT_API_KEY is set")

        # Check if GCS bucket is configured with fallback and detailed logging
        logger.info("Checking for GCS_BUCKET_NAME...")
        bucket_name = os.getenv('GCS_BUCKET_NAME')
        if not bucket_name:
            logger.warning("GCS_BUCKET_NAME environment variable not set, using fallback")
            # Fallback to a hardcoded name
            bucket_name = "raw-data-external-source-437915-1"

        logger.info(f"Using bucket: {bucket_name}")

        # Get date range (past 2 days)
        from_date, to_date = get_date_range()
        logger.info(f"Processing data from {from_date} to {to_date}")

        # Initialize components with explicit error handling
        try:
            logger.info("Initializing BillomatClient...")
            client = BillomatClient(api_key)
            logger.info("BillomatClient initialized successfully")

            logger.info("Initializing BillomatDataProcessor...")
            processor = BillomatDataProcessor()
            logger.info("BillomatDataProcessor initialized successfully")

            logger.info("Initializing BillomatGCSStorage...")
            storage = BillomatGCSStorage()
            logger.info("BillomatGCSStorage initialized successfully")
        except Exception as init_error:
            logger.error(f"Error initializing components: {str(init_error)}")
            logger.error(traceback.format_exc())
            raise

        # Process main data entities
        entities_processed = []

        # Process invoices
        try:
            logger.info("Starting to process invoices...")
            process_invoices(client, processor, storage, from_date, to_date)
            entities_processed.append("invoices")
            logger.info("Successfully processed invoices")
        except Exception as e:
            logger.error(f"Error processing invoices: {str(e)}")
            logger.error(traceback.format_exc())

        # Process credit notes
        try:
            logger.info("Starting to process credit notes...")
            process_credit_notes(client, processor, storage, from_date, to_date)
            entities_processed.append("credit_notes")
            logger.info("Successfully processed credit notes")
        except Exception as e:
            logger.error(f"Error processing credit notes: {str(e)}")
            logger.error(traceback.format_exc())

        # Process clients - now includes date filtering
        try:
            logger.info("Starting to process clients...")
            process_clients(client, processor, storage, from_date, to_date)
            entities_processed.append("clients")
            logger.info("Successfully processed clients")
        except Exception as e:
            logger.error(f"Error processing clients: {str(e)}")
            logger.error(traceback.format_exc())

        # Process articles - now includes date filtering
        try:
            logger.info("Starting to process articles...")
            process_articles(client, processor, storage, from_date, to_date)
            entities_processed.append("articles")
            logger.info("Successfully processed articles")
        except Exception as e:
            logger.error(f"Error processing articles: {str(e)}")
            logger.error(traceback.format_exc())

        # Check if today is the last day of the month to run month-end invoice status check
        today = datetime.now().date()
        _, last_day = calendar.monthrange(today.year, today.month)

        if today.day == last_day:
            try:
                logger.info("Today is the last day of the month, running invoice status check...")
                storage.check_month_end_invoice_updates(client)
                entities_processed.append("month_end_invoice_check")
                logger.info("Successfully completed month-end invoice check")
            except Exception as e:
                logger.error(f"Error performing month-end invoice check: {str(e)}")
                logger.error(traceback.format_exc())

        # Evaluate overall success
        if not entities_processed:
            logger.error("No entities were successfully processed")
            raise Exception("No entities were successfully processed")

        logger.info(f"Billomat ingestion completed. Processed entities: {', '.join(entities_processed)}")
        return {
            "status": "success",
            "processed_entities": entities_processed,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in Billomat ingestion: {str(e)}")
        logger.error(traceback.format_exc())
        # Re-raise the exception to mark the function as failed
        raise


def process_invoices(client, processor, storage, from_date, to_date):
    """Process and store invoices with change detection."""
    try:
        logger.info("Processing invoices")

        # Get last sync time
        last_sync = storage.get_last_sync_time()
        logger.info(f"Last sync time: {last_sync or 'Never'}")

        # Get invoices (using last_sync if available)
        invoices_data = client.get_invoices(from_date, to_date, last_sync)

        import xml.etree.ElementTree as ET
        root = ET.fromstring(invoices_data)

        invoice_count = 0
        processed_count = 0
        total_invoices = len(root.findall('.//invoice'))
        logger.info(f"Found {total_invoices} invoices to process")

        for invoice_elem in root.findall('.//invoice'):
            try:
                invoice_id = invoice_elem.find('id').text
                logger.info(f"Processing invoice {invoice_id} ({invoice_count+1}/{total_invoices})")

                # Get detailed invoice data
                detailed_data = client.get_invoice(invoice_id)

                # Get PDF data
                pdf_data = client.get_invoice_pdf(invoice_id)

                # Process data
                processed_data = processor.process_data(
                    detailed_data,
                    data_type='invoice',
                    pdf_metadata=pdf_data.get('metadata') if pdf_data else None
                )

                # Store data (with change detection)
                storage.store_invoice_complete(processed_data, pdf_data)

                invoice_count += 1
                processed_count += 1

            except Exception as e:
                logger.error(f"Error processing invoice {invoice_id}: {str(e)}")
                logger.error(traceback.format_exc())
                continue

        logger.info(f"Processed {invoice_count} invoices, {processed_count} were new or updated")

    except Exception as e:
        logger.error(f"Error in invoice processing: {str(e)}")
        logger.error(traceback.format_exc())
        raise


def process_credit_notes(client, processor, storage, from_date, to_date):
    """Process and store credit notes for the specified date range."""
    try:
        logger.info(f"Processing credit notes from {from_date} to {to_date}")

        logger.info("Fetching credit notes data from Billomat API...")
        credit_notes_data = client.get_credit_notes(from_date, to_date)
        logger.info(f"Received credit notes data of length: {len(credit_notes_data) if credit_notes_data else 0}")

        import xml.etree.ElementTree as ET
        root = ET.fromstring(credit_notes_data)

        credit_note_count = 0
        total_credit_notes = len(root.findall('.//credit-note'))
        logger.info(f"Found {total_credit_notes} credit notes to process")

        for credit_note_elem in root.findall('.//credit-note'):
            try:
                credit_note_id = credit_note_elem.find('id').text
                logger.info(f"Processing credit note {credit_note_id} ({credit_note_count+1}/{total_credit_notes})")

                # Get client ID from the credit note
                client_id_elem = credit_note_elem.find('client_id')
                client_id = client_id_elem.text if client_id_elem is not None else None
                client_data = None

                # Fetch client data if client_id is available
                if client_id:
                    logger.info(f"Fetching client data for client ID: {client_id}")
                    client_data = client.get_client(client_id)

                    if client_data:
                        logger.info(f"Successfully retrieved client data for credit note {credit_note_id}")
                    else:
                        logger.warning(f"No client data returned for client ID: {client_id}")
                        # Continue processing without client data
                else:
                    logger.warning(f"No client ID found for credit note {credit_note_id}")

                logger.info(f"Fetching detailed data for credit note {credit_note_id}")
                detailed_data = client.get_credit_note(credit_note_id)

                logger.info(f"Processing data for credit note {credit_note_id}")
                processed_data = processor.process_credit_note(detailed_data, client_data)

                logger.info(f"Storing data for credit note {credit_note_id}")
                storage.store_credit_note(processed_data)

                logger.info(f"Successfully processed credit note {credit_note_id}")
                credit_note_count += 1

            except Exception as e:
                logger.error(f"Error processing credit note {credit_note_id}: {str(e)}")
                logger.error(traceback.format_exc())
                continue

        logger.info(f"Processed {credit_note_count} credit notes successfully")

    except Exception as e:
        logger.error(f"Error in credit notes processing: {str(e)}")
        logger.error(traceback.format_exc())
        raise


def process_articles(client, processor, storage, from_date, to_date):
    """
    Process and store articles data with date filtering.
    Retrieves all articles but only stores those created between from_date and to_date.

    Args:
        client (BillomatClient): Client to fetch data
        processor (BillomatDataProcessor): Processor to process data
        storage (BillomatGCSStorage): Storage to store data
        from_date (str): Start date for filtering in YYYY-MM-DD format
        to_date (str): End date for filtering in YYYY-MM-DD format
    """
    try:
        logger.info(f"Processing articles with date filtering from {from_date} to {to_date}")

        logger.info("Fetching articles data from Billomat API...")
        articles_data = client.get_articles()
        logger.info(f"Received articles data of length: {len(articles_data) if articles_data else 0}")

        # Convert string dates to datetime objects if they're strings
        from_date_obj = from_date
        to_date_obj = to_date

        if isinstance(from_date, str):
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d')

        if isinstance(to_date, str):
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d')
            # Set to end of day for inclusive comparison
            to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)

        logger.info("Processing articles data with date filtering...")
        processed_data = processor.process_data(
            articles_data,
            data_type='article',
            from_date=from_date_obj,
            to_date=to_date_obj
        )

        # Check if the total_articles key exists and has a valid value
        if processed_data and 'data' in processed_data and 'metadata' in processed_data['data'] and 'total_articles' in processed_data['data']['metadata']:
            logger.info(f"Filtered to {processed_data['data']['metadata']['total_articles']} articles within date range")
        else:
            logger.info("Processed articles data but could not determine article count")

        logger.info("Storing articles data...")
        storage.store_articles(processed_data)

        logger.info("Successfully processed articles")

    except Exception as e:
        logger.error(f"Error in articles processing: {str(e)}")
        logger.error(traceback.format_exc())
        raise


def process_clients(client, processor, storage, from_date=None, to_date=None):
    """
    Process and store clients data from the last pages.
    Retrieves the last 3 pages of clients without date filtering.

    Args:
        client (BillomatClient): Client to fetch data
        processor (BillomatDataProcessor): Processor to process data
        storage (BillomatGCSStorage): Storage to store data
        from_date (str, optional): Not used for filtering, kept for API compatibility
        to_date (str, optional): Not used for filtering, kept for API compatibility
    """
    try:
        logger.info("Processing clients data (retrieving last 3 pages)")

        # Try to get clients with different page sizes if needed
        max_attempts = 3
        for attempt in range(1, max_attempts + 1):
            logger.info(f"Attempt {attempt}/{max_attempts}: Fetching clients data from Billomat API (last 3 pages)...")

            # Adjust page size based on attempt number
            per_page = 100 if attempt == 1 else (50 if attempt == 2 else 20)

            clients_data = client.get_clients(get_last_pages=True, num_pages=3, per_page=per_page)
            data_length = len(clients_data) if clients_data else 0
            logger.info(f"Received clients data of length: {data_length}")

            # If we got some data, process it without date filtering
            if data_length > 0 and b"<client>" in clients_data:
                logger.info(f"Successfully retrieved client data on attempt {attempt}")
                break

            if attempt < max_attempts:
                logger.warning(f"No client data found on attempt {attempt}, trying with different parameters...")

        logger.info("Processing clients data without date filtering...")
        processed_data = processor.process_data(
            clients_data,
            data_type='client'
            # No date filtering parameters
        )

        # Check if the total_clients key exists and has a valid value
        if processed_data and 'data' in processed_data and 'metadata' in processed_data['data'] and 'total_clients' in processed_data['data']['metadata']:
            logger.info(f"Filtered to {processed_data['data']['metadata']['total_clients']} clients within date range")
        else:
            logger.info("Processed clients data but could not determine client count")

        logger.info("Storing clients data...")
        storage.store_clients(processed_data)

        logger.info("Successfully processed clients")

    except Exception as e:
        logger.error(f"Error in clients processing: {str(e)}")
        logger.error(traceback.format_exc())
        raise


# For running as Cloud Run service
if __name__ == "__main__":
    from flask import Flask, request

    app = Flask(__name__)

    @app.route('/', methods=['GET', 'POST'])
    def handle_request():
        return billomat_http_trigger(request)

    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port)