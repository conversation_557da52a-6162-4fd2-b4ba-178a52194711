# """
# Billomat-specific implementation of the GCS Storage Manager
# Handles storage operations for invoices, articles, clients, and credit notes with the updated metadata mechanism
# """
# from typing import Any, Dict, Optional, List
# import json
# import logging
# import traceback
# import os
# from datetime import datetime, date
# import calendar

# from src.infrastructure.gcs.base_storage import BaseGCSStorageManager
# from src.infrastructure.gcs.client import GCSClient
# from src.infrastructure.gcs.metadata_manager import MetadataManager
# from src.infrastructure.monitoring.logging import setup_logger

# logger = setup_logger(__name__)

# class BillomatGCSStorage(BaseGCSStorageManager):
#     """Manages GCS storage operations for Billomat data."""
    
#     def __init__(self, gcs_client: Optional[GCSClient] = None):
#         """
#         Initialize the Billomat storage manager.
        
#         Args:
#             gcs_client (Optional[GCSClient]): GCS client instance. If None, creates new instance.
#         """
#         logger.info("Initializing BillomatGCSStorage")
#         try:
#             # Override bucket name with environment variable if specified
#             bucket_name = os.getenv('GCS_BUCKET_NAME')
#             if bucket_name:
#                 logger.info(f"Using bucket name from environment: {bucket_name}")
#                 if gcs_client is None:
#                     logger.info("Creating new GCSClient with specified bucket")
#                     gcs_client = GCSClient(bucket_name=bucket_name)
            
#             # Initialize the base class
#             super().__init__(gcs_client)
#             logger.info(f"Initialized BaseGCSStorageManager with bucket: {self.bucket.name if self.bucket else 'None'}")
            
#             # Initialize metadata manager
#             self.metadata_manager = MetadataManager(self, "cbtouristenkarten-billomat")
#             logger.info("Initialized metadata manager")
            
#             # Setup invoice status metadata paths
#             self.metadata_base_path = "cbtouristenkarten-billomat/metadata"
#             self.paid_invoices_path = f"{self.metadata_base_path}/paid_invoices_metadata.json"
#             self.unpaid_invoices_path = f"{self.metadata_base_path}/unpaid_invoices_metadata.json"
            
#             logger.info("BillomatGCSStorage initialized successfully")
            
#         except Exception as e:
#             logger.error(f"Error initializing BillomatGCSStorage: {str(e)}")
#             logger.error(traceback.format_exc())
#             raise
    
#     def store_invoice_complete(self, processed_data: Dict, pdf_data: Optional[Dict] = None) -> None:
#         """
#         Store all invoice-related data with change detection.
        
#         Args:
#             processed_data (Dict): Processed invoice data with paths and metadata
#             pdf_data (Optional[Dict]): PDF data including base64 content and metadata
#         """
#         try:
#             logger.info("Starting store_invoice_complete")
            
#             # Extract key invoice data
#             invoice_id = processed_data['metadata'].get('id', 'unknown')
#             invoice_number = processed_data['metadata'].get('invoice_number', 'unknown')
            
#             # Check for changes using key properties
#             checksum_data = {
#                 'status': processed_data['metadata'].get('status'),
#                 'total_amount': processed_data['metadata'].get('total_gross'),
#                 'paid_amount': processed_data['metadata'].get('paid_amount')
#             }
            
#             # Check if invoice has changed
#             has_changed = self.metadata_manager.has_entity_changed('invoice', invoice_id, checksum_data)
            
#             if has_changed:
#                 logger.info(f"Invoice {invoice_number} (ID: {invoice_id}) has changed or is new, storing updates")
                
#                 # Store invoice dataset
#                 dataset_path = processed_data['paths']['dataset']
#                 logger.info(f"Storing invoice dataset at: {dataset_path}")
                
#                 self.store_data(
#                     dataset_path,
#                     json.dumps(processed_data['metadata'], indent=2)
#                 )
#                 logger.info("Successfully stored invoice dataset")
                
#                 # Store PDF if available
#                 if pdf_data and 'pdf_base64' in pdf_data:
#                     # Store base64 encoded PDF
#                     pdf_path = processed_data['paths']['pdf']
#                     logger.info(f"Storing PDF at: {pdf_path}")
                    
#                     self.store_data(
#                         pdf_path,
#                         pdf_data['pdf_base64'],
#                         'text/plain'  # Base64 stored as text
#                     )
#                     logger.info("Successfully stored PDF")
                    
#                     # Store PDF metadata
#                     if 'metadata' in pdf_data:
#                         pdf_metadata_path = processed_data['paths']['pdf_metadata']
#                         logger.info(f"Storing PDF metadata at: {pdf_metadata_path}")
                        
#                         self.store_data(
#                             pdf_metadata_path,
#                             json.dumps(pdf_data['metadata'], indent=2)
#                         )
#                         logger.info("Successfully stored PDF metadata")
#                 else:
#                     logger.info("No PDF data available to store")
                
#                 # Update metadata
#                 logger.info(f"Updating metadata for invoice: {invoice_id}")
#                 self.metadata_manager.update_entity_checksum('invoice', invoice_id, 
#                                                            self.metadata_manager.calculate_checksum(checksum_data))
                
#                 # Update paid/unpaid invoice metadata files
#                 self.update_invoice_status_metadata(processed_data['metadata'])
                
#                 # Also update last sync time at each successful operation
#                 self.metadata_manager.update_last_sync_time()
                
#                 logger.info(f"Successfully processed and stored invoice {invoice_number}")
#             else:
#                 logger.info(f"Invoice {invoice_number} (ID: {invoice_id}) has not changed, skipping storage")
            
#         except Exception as e:
#             logger.error(f"Error in store_invoice_complete: {str(e)}")
#             logger.error(traceback.format_exc())
#             raise

#     # Apply similar change detection logic to other methods
#     def store_articles(self, processed_data: Dict) -> None:
#         """
#         Store articles data as JSON with change detection.
        
#         Args:
#             processed_data (Dict): Processed articles data with path and content
#         """
#         try:
#             logger.info("Starting store_articles")
            
#             # Create a checksum of the entire articles dataset
#             checksum_data = {
#                 'count': len(processed_data['data']['articles']) if 'articles' in processed_data['data'] else 0,
#                 'timestamp': datetime.now().isoformat()
#             }
            
#             # Check if articles data has changed
#             has_changed = self.metadata_manager.has_entity_changed('articles', 'all', checksum_data)
            
#             if has_changed:
#                 logger.info(f"Articles data has changed, storing updates")
                
#                 logger.info(f"Storing articles data at: {processed_data['path']}")
#                 self.store_data(
#                     processed_data['path'],
#                     json.dumps(processed_data['data'], indent=2),
#                     'application/json'
#                 )
                
#                 # Update metadata
#                 article_count = len(processed_data['data']['articles']) if 'articles' in processed_data['data'] else 0
#                 logger.info(f"Updating metadata for {article_count} articles")
                
#                 self.metadata_manager.update_entity_checksum('articles', 'all', 
#                                                           self.metadata_manager.calculate_checksum(checksum_data))
                
#                 # Also update last sync time
#                 self.metadata_manager.update_last_sync_time()
                
#                 logger.info(f"Successfully stored articles data")
#             else:
#                 logger.info("Articles data has not changed, skipping storage")
            
#         except Exception as e:
#             logger.error(f"Error storing articles data: {str(e)}")
#             logger.error(traceback.format_exc())
#             raise

#     # Same pattern for clients
#     def store_clients(self, processed_data: Dict) -> None:
#         """
#         Store clients data with change detection.
        
#         Args:
#             processed_data (Dict): Processed client data with paths and content
#         """
#         try:
#             logger.info("Starting store_clients")
            
#             # Create a checksum of the entire clients dataset
#             checksum_data = {
#                 'count': len(processed_data['data']['clients']) if 'clients' in processed_data['data'] else 0,
#                 'timestamp': datetime.now().isoformat()
#             }
            
#             # Check if clients data has changed
#             has_changed = self.metadata_manager.has_entity_changed('clients', 'all', checksum_data)
            
#             if has_changed:
#                 logger.info(f"Clients data has changed, storing updates")
                
#                 logger.info(f"Storing clients data at: {processed_data['path']}")
#                 self.store_data(
#                     processed_data['path'],
#                     json.dumps(processed_data['data'], indent=2),
#                     'application/json'
#                 )
                
#                 # Update metadata
#                 client_count = len(processed_data['data']['clients']) if 'clients' in processed_data['data'] else 0
#                 logger.info(f"Updating metadata for {client_count} clients")
                
#                 self.metadata_manager.update_entity_checksum('clients', 'all', 
#                                                           self.metadata_manager.calculate_checksum(checksum_data))
                
#                 # Also update last sync time
#                 self.metadata_manager.update_last_sync_time()
                
#                 logger.info(f"Successfully stored clients data")
#             else:
#                 logger.info("Clients data has not changed, skipping storage")
            
#         except Exception as e:
#             logger.error(f"Error storing clients data: {str(e)}")
#             logger.error(traceback.format_exc())
#             raise

#     # And for credit notes
#     def store_credit_note(self, processed_data: Dict) -> None:
#         """
#         Store credit note data with change detection.
        
#         Args:
#             processed_data (Dict): Processed credit note data with paths and metadata
#         """
#         try:
#             logger.info("Starting store_credit_note")
            
#             # Extract key credit note data
#             credit_note_id = processed_data['metadata'].get('id', 'unknown')
#             credit_note_number = processed_data['metadata'].get('credit_note_number', 'unknown')
            
#             # Check for changes using key properties
#             checksum_data = {
#                 'status': processed_data['metadata'].get('status'),
#                 'total_amount': processed_data['metadata'].get('total_gross')
#             }
            
#             # Check if credit note has changed
#             has_changed = self.metadata_manager.has_entity_changed('credit_note', credit_note_id, checksum_data)
            
#             if has_changed:
#                 logger.info(f"Credit note {credit_note_number} (ID: {credit_note_id}) has changed or is new, storing updates")
                
#                 logger.info(f"Storing credit note data at: {processed_data['paths']['dataset']}")
#                 self.store_data(
#                     processed_data['paths']['dataset'],
#                     json.dumps(processed_data['metadata'], indent=2),
#                     'application/json'
#                 )
                
#                 # Update metadata
#                 logger.info(f"Updating metadata for credit note: {credit_note_id}")
#                 self.metadata_manager.update_entity_checksum('credit_note', credit_note_id, 
#                                                            self.metadata_manager.calculate_checksum(checksum_data))
                
#                 # Also update last sync time
#                 self.metadata_manager.update_last_sync_time()
                
#                 logger.info(f"Successfully stored credit note data")
#             else:
#                 logger.info(f"Credit note {credit_note_number} (ID: {credit_note_id}) has not changed, skipping storage")
            
#         except Exception as e:
#             logger.error(f"Error storing credit note data: {str(e)}")
#             logger.error(traceback.format_exc())
#             raise
    
#     def get_last_sync_time(self) -> Optional[str]:
#         """
#         Get the timestamp of the last successful sync
        
#         Returns:
#             ISO format timestamp or None if never synced
#         """
#         return self.metadata_manager.get_last_sync_time()
        
#     def update_invoice_status_metadata(self, invoice_data: Dict) -> None:
#         """
#         Update the paid/unpaid invoice metadata files based on the invoice status.
        
#         Args:
#             invoice_data (Dict): The invoice metadata
#         """
#         try:
#             logger.info("Updating invoice status metadata")
            
#             # Extract key invoice info
#             invoice_id = invoice_data.get('id', 'unknown')
#             invoice_number = invoice_data.get('invoice_number', 'unknown')
#             status = invoice_data.get('status', 'unknown')
#             total_gross = invoice_data.get('total_gross', '0')
#             client_id = invoice_data.get('client_id', 'unknown')
#             invoice_date = invoice_data.get('date', 'unknown')
#             created_date = invoice_data.get('created', 'unknown')
            
#             # Create invoice summary for metadata
#             invoice_summary = {
#                 'id': invoice_id,
#                 'invoice_number': invoice_number,
#                 'status': status,
#                 'total_gross': total_gross,
#                 'client_id': client_id,
#                 'date': invoice_date,
#                 'created': created_date,
#                 'last_updated': datetime.now().isoformat()
#             }
            
#             # Determine which metadata file to update based on status
#             if status == 'paid':
#                 # This is a paid invoice, add to paid metadata and remove from unpaid if present
#                 self._update_paid_invoices_metadata(invoice_summary)
#                 self._remove_from_unpaid_invoices_metadata(invoice_id)
#             else:
#                 # This is an unpaid invoice (draft, open, overdue, etc.), add to unpaid metadata
#                 self._update_unpaid_invoices_metadata(invoice_summary)
#                 self._remove_from_paid_invoices_metadata(invoice_id)
                
#             logger.info(f"Updated invoice status metadata for invoice {invoice_number} with status {status}")
            
#         except Exception as e:
#             logger.error(f"Error updating invoice status metadata: {str(e)}")
#             logger.error(traceback.format_exc())
#             # Don't raise to avoid interrupting main process
    
#     def _update_paid_invoices_metadata(self, invoice_summary: Dict) -> None:
#         """
#         Update the paid invoices metadata file.
        
#         Args:
#             invoice_summary (Dict): Invoice summary data
#         """
#         try:
#             # Get existing metadata or create new
#             paid_invoices = self._get_metadata_file(self.paid_invoices_path)
            
#             if not paid_invoices:
#                 # Initialize new metadata file structure
#                 paid_invoices = {
#                     'last_updated': datetime.now().isoformat(),
#                     'invoices': {}
#                 }
            
#             # Update/add this invoice
#             paid_invoices['invoices'][invoice_summary['id']] = invoice_summary
#             paid_invoices['last_updated'] = datetime.now().isoformat()
            
#             # Store updated metadata
#             self.store_data(
#                 self.paid_invoices_path,
#                 json.dumps(paid_invoices, indent=2)
#             )
            
#             logger.info(f"Updated paid invoices metadata with invoice {invoice_summary['invoice_number']}")
            
#         except Exception as e:
#             logger.error(f"Error updating paid invoices metadata: {str(e)}")
#             logger.error(traceback.format_exc())
    
#     def _update_unpaid_invoices_metadata(self, invoice_summary: Dict) -> None:
#         """
#         Update the unpaid invoices metadata file.
        
#         Args:
#             invoice_summary (Dict): Invoice summary data
#         """
#         try:
#             # Get existing metadata or create new
#             unpaid_invoices = self._get_metadata_file(self.unpaid_invoices_path)
            
#             if not unpaid_invoices:
#                 # Initialize new metadata file structure
#                 unpaid_invoices = {
#                     'last_updated': datetime.now().isoformat(),
#                     'invoices': {}
#                 }
            
#             # Update/add this invoice
#             unpaid_invoices['invoices'][invoice_summary['id']] = invoice_summary
#             unpaid_invoices['last_updated'] = datetime.now().isoformat()
            
#             # Store updated metadata
#             self.store_data(
#                 self.unpaid_invoices_path,
#                 json.dumps(unpaid_invoices, indent=2)
#             )
            
#             logger.info(f"Updated unpaid invoices metadata with invoice {invoice_summary['invoice_number']}")
            
#         except Exception as e:
#             logger.error(f"Error updating unpaid invoices metadata: {str(e)}")
#             logger.error(traceback.format_exc())
    
#     def _remove_from_paid_invoices_metadata(self, invoice_id: str) -> None:
#         """
#         Remove an invoice from the paid invoices metadata file.
        
#         Args:
#             invoice_id (str): ID of the invoice to remove
#         """
#         try:
#             # Get existing metadata
#             paid_invoices = self._get_metadata_file(self.paid_invoices_path)
            
#             if not paid_invoices or 'invoices' not in paid_invoices:
#                 # Nothing to remove
#                 return
            
#             # Remove this invoice if present
#             if invoice_id in paid_invoices['invoices']:
#                 del paid_invoices['invoices'][invoice_id]
#                 paid_invoices['last_updated'] = datetime.now().isoformat()
                
#                 # Store updated metadata
#                 self.store_data(
#                     self.paid_invoices_path,
#                     json.dumps(paid_invoices, indent=2)
#                 )
                
#                 logger.info(f"Removed invoice {invoice_id} from paid invoices metadata")
            
#         except Exception as e:
#             logger.error(f"Error removing from paid invoices metadata: {str(e)}")
#             logger.error(traceback.format_exc())
    
#     def _remove_from_unpaid_invoices_metadata(self, invoice_id: str) -> None:
#         """
#         Remove an invoice from the unpaid invoices metadata file.
        
#         Args:
#             invoice_id (str): ID of the invoice to remove
#         """
#         try:
#             # Get existing metadata
#             unpaid_invoices = self._get_metadata_file(self.unpaid_invoices_path)
            
#             if not unpaid_invoices or 'invoices' not in unpaid_invoices:
#                 # Nothing to remove
#                 return
            
#             # Remove this invoice if present
#             if invoice_id in unpaid_invoices['invoices']:
#                 del unpaid_invoices['invoices'][invoice_id]
#                 unpaid_invoices['last_updated'] = datetime.now().isoformat()
                
#                 # Store updated metadata
#                 self.store_data(
#                     self.unpaid_invoices_path,
#                     json.dumps(unpaid_invoices, indent=2)
#                 )
                
#                 logger.info(f"Removed invoice {invoice_id} from unpaid invoices metadata")
            
#         except Exception as e:
#             logger.error(f"Error removing from unpaid invoices metadata: {str(e)}")
#             logger.error(traceback.format_exc())
    
#     def _get_metadata_file(self, path: str) -> Optional[Dict]:
#         """
#         Get the contents of a metadata file.
        
#         Args:
#             path (str): Path to the metadata file
            
#         Returns:
#             Dict or None: Contents of the metadata file, or None if not found
#         """
#         try:
#             data = self.get_data(path)
#             if data:
#                 return json.loads(data)
#             return None
#         except Exception as e:
#             logger.warning(f"Error reading metadata file {path}: {str(e)}")
#             return None
            
#     def check_month_end_invoice_updates(self, client) -> None:
#         """
#         Monthly check to update invoice status changes.
#         This should be run on the last day of each month to check for status changes.
        
#         Args:
#             client: BillomatClient instance for API access
#         """
#         try:
#             logger.info("Starting month-end invoice status check")
            
#             # Check if today is the last day of the month
#             today = datetime.now().date()
#             _, last_day = calendar.monthrange(today.year, today.month)
            
#             if today.day != last_day:
#                 logger.info(f"Today ({today}) is not the last day of the month. Skipping month-end check.")
#                 return
            
#             # Get unpaid invoices metadata
#             unpaid_invoices = self._get_metadata_file(self.unpaid_invoices_path)
            
#             if not unpaid_invoices or 'invoices' not in unpaid_invoices or not unpaid_invoices['invoices']:
#                 logger.info("No unpaid invoices to check.")
#                 return
            
#             logger.info(f"Checking {len(unpaid_invoices['invoices'])} unpaid invoices for status changes")
            
#             # Track invoices that need to be updated
#             invoices_to_update = []
            
#             # Check each unpaid invoice for status changes
#             for invoice_id, invoice_data in unpaid_invoices['invoices'].items():
#                 try:
#                     # Get latest invoice data from API
#                     current_invoice_xml = client.get_invoice(invoice_id)
                    
#                     # Process invoice data
#                     from src.data_pipelines.billomat.processor import BillomatDataProcessor
#                     processor = BillomatDataProcessor()
                    
#                     # Convert XML to dictionary
#                     current_invoice_data = BillomatDataProcessor.xml_to_dict(current_invoice_xml)
                    
#                     # Check for changes in status, total amount, or other key fields
#                     has_changed = (
#                         current_invoice_data.get('status') != invoice_data.get('status') or
#                         current_invoice_data.get('total_gross') != invoice_data.get('total_gross')
#                     )
                    
#                     if has_changed:
#                         logger.info(f"Invoice {invoice_data['invoice_number']} has changed. Adding to update list.")
                        
#                         # Get PDF if available
#                         pdf_data = None
#                         try:
#                             pdf_data = client.get_invoice_pdf(invoice_id)
#                         except Exception as pdf_err:
#                             logger.warning(f"Could not retrieve PDF for invoice {invoice_id}: {str(pdf_err)}")
                        
#                         # Process invoice data for storage
#                         processed_data = processor.process_invoice(current_invoice_xml, 
#                                                               pdf_metadata=pdf_data.get('metadata') if pdf_data else None)
                        
#                         # Add to update list
#                         invoices_to_update.append((processed_data, pdf_data))
                        
#                 except Exception as e:
#                     logger.error(f"Error checking invoice {invoice_id}: {str(e)}")
#                     logger.error(traceback.format_exc())
#                     continue
            
#             # Update invoices with changes
#             logger.info(f"Found {len(invoices_to_update)} invoices with changes to update")
            
#             for processed_data, pdf_data in invoices_to_update:
#                 try:
#                     # Store updated invoice data
#                     self.store_invoice_complete(processed_data, pdf_data)
#                     logger.info(f"Successfully updated invoice {processed_data['metadata'].get('invoice_number')}")
#                 except Exception as e:
#                     logger.error(f"Error updating invoice {processed_data['metadata'].get('id')}: {str(e)}")
#                     logger.error(traceback.format_exc())
            
#             logger.info("Completed month-end invoice status check")
            
#         except Exception as e:
#             logger.error(f"Error during month-end invoice check: {str(e)}")
#             logger.error(traceback.format_exc())




"""
Fixed implementation to ensure proper separation of paid and unpaid invoices in metadata files
"""
from typing import Any, Dict, Optional, List
import json
import logging
import traceback
import os
from datetime import datetime, date
import calendar

from src.infrastructure.gcs.base_storage import BaseGCSStorageManager
from src.infrastructure.gcs.client import GCSClient
from src.infrastructure.gcs.metadata_manager import MetadataManager
from src.infrastructure.monitoring.logging import setup_logger

logger = setup_logger(__name__)

class BillomatGCSStorage(BaseGCSStorageManager):
    """Manages GCS storage operations for Billomat data."""
    
    def __init__(self, gcs_client: Optional[GCSClient] = None):
        """
        Initialize the Billomat storage manager.
        
        Args:
            gcs_client (Optional[GCSClient]): GCS client instance. If None, creates new instance.
        """
        logger.info("Initializing BillomatGCSStorage")
        try:
            # Override bucket name with environment variable if specified
            bucket_name = os.getenv('GCS_BUCKET_NAME')
            if bucket_name:
                logger.info(f"Using bucket name from environment: {bucket_name}")
                if gcs_client is None:
                    logger.info("Creating new GCSClient with specified bucket")
                    gcs_client = GCSClient(bucket_name=bucket_name)
            
            # Initialize the base class
            super().__init__(gcs_client)
            logger.info(f"Initialized BaseGCSStorageManager with bucket: {self.bucket.name if self.bucket else 'None'}")
            
            # Initialize metadata manager
            self.metadata_manager = MetadataManager(self, "cbtouristenkarten-billomat")
            logger.info("Initialized metadata manager")
            
            # Setup invoice status metadata paths
            self.metadata_base_path = "cbtouristenkarten-billomat/metadata"
            self.paid_invoices_path = f"{self.metadata_base_path}/paid_invoices_metadata.json"
            self.unpaid_invoices_path = f"{self.metadata_base_path}/unpaid_invoices_metadata.json"
            
            # Create metadata directory if it doesn't exist
            self._ensure_metadata_files_exist()
            
            logger.info("BillomatGCSStorage initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing BillomatGCSStorage: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def _ensure_metadata_files_exist(self):
        """Ensure that both metadata files exist with proper structure."""
        try:
            # Create or verify paid invoices metadata file
            paid_invoices = self._get_metadata_file(self.paid_invoices_path)
            if not paid_invoices:
                # Initialize with empty structure
                paid_invoices = {
                    "last_updated": datetime.now().isoformat(),
                    "invoices": {}
                }
                self.store_data(
                    self.paid_invoices_path,
                    json.dumps(paid_invoices, indent=2)
                )
                logger.info(f"Created initial paid invoices metadata file at {self.paid_invoices_path}")
            
            # Create or verify unpaid invoices metadata file
            unpaid_invoices = self._get_metadata_file(self.unpaid_invoices_path)
            if not unpaid_invoices:
                # Initialize with empty structure
                unpaid_invoices = {
                    "last_updated": datetime.now().isoformat(),
                    "invoices": {}
                }
                self.store_data(
                    self.unpaid_invoices_path,
                    json.dumps(unpaid_invoices, indent=2)
                )
                logger.info(f"Created initial unpaid invoices metadata file at {self.unpaid_invoices_path}")
        
        except Exception as e:
            logger.error(f"Error ensuring metadata files exist: {str(e)}")
            logger.error(traceback.format_exc())
    
    def store_invoice_complete(self, processed_data: Dict, pdf_data: Optional[Dict] = None) -> None:
        """
        Store all invoice-related data with change detection.
        
        Args:
            processed_data (Dict): Processed invoice data with paths and metadata
            pdf_data (Optional[Dict]): PDF data including base64 content and metadata
        """
        try:
            logger.info("Starting store_invoice_complete")
            
            # Extract key invoice data
            invoice_id = processed_data['metadata'].get('id', 'unknown')
            invoice_number = processed_data['metadata'].get('invoice_number', 'unknown')
            
            # Check for changes using key properties
            checksum_data = {
                'status': processed_data['metadata'].get('status'),
                'total_amount': processed_data['metadata'].get('total_gross'),
                'paid_amount': processed_data['metadata'].get('paid_amount')
            }
            
            # Check if invoice has changed
            has_changed = self.metadata_manager.has_entity_changed('invoice', invoice_id, checksum_data)
            
            # ALWAYS update invoice status metadata regardless of content changes
            logger.info(f"Updating status metadata for invoice {invoice_number} (ID: {invoice_id})")
            self.update_invoice_status_metadata(processed_data['metadata'])
            
            if has_changed:
                logger.info(f"Invoice {invoice_number} (ID: {invoice_id}) has changed or is new, storing updates")
                
                # Store invoice dataset
                dataset_path = processed_data['paths']['dataset']
                logger.info(f"Storing invoice dataset at: {dataset_path}")
                
                self.store_data(
                    dataset_path,
                    json.dumps(processed_data['metadata'], indent=2)
                )
                logger.info("Successfully stored invoice dataset")
                
                # Store PDF if available
                if pdf_data and 'pdf_base64' in pdf_data:
                    # Store base64 encoded PDF
                    pdf_path = processed_data['paths']['pdf']
                    logger.info(f"Storing PDF at: {pdf_path}")
                    
                    self.store_data(
                        pdf_path,
                        pdf_data['pdf_base64'],
                        'text/plain'  # Base64 stored as text
                    )
                    logger.info("Successfully stored PDF")
                    
                    # Store PDF metadata
                    if 'metadata' in pdf_data:
                        pdf_metadata_path = processed_data['paths']['pdf_metadata']
                        logger.info(f"Storing PDF metadata at: {pdf_metadata_path}")
                        
                        self.store_data(
                            pdf_metadata_path,
                            json.dumps(pdf_data['metadata'], indent=2)
                        )
                        logger.info("Successfully stored PDF metadata")
                else:
                    logger.info("No PDF data available to store")
                
                # Update metadata
                logger.info(f"Updating metadata for invoice: {invoice_id}")
                self.metadata_manager.update_entity_checksum('invoice', invoice_id, 
                                                           self.metadata_manager.calculate_checksum(checksum_data))
                
                # Also update last sync time at each successful operation
                self.metadata_manager.update_last_sync_time()
                
                logger.info(f"Successfully processed and stored invoice {invoice_number}")
            else:
                logger.info(f"Invoice {invoice_number} (ID: {invoice_id}) has not changed, skipping storage but status metadata was updated")
            
        except Exception as e:
            logger.error(f"Error in store_invoice_complete: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    # Apply similar change detection logic to other methods
    def store_articles(self, processed_data: Dict) -> None:
        """
        Store articles data as JSON with change detection.
        
        Args:
            processed_data (Dict): Processed articles data with path and content
        """
        try:
            logger.info("Starting store_articles")
            
            # Create a checksum of the entire articles dataset
            checksum_data = {
                'count': len(processed_data['data']['articles']) if 'articles' in processed_data['data'] else 0,
                'timestamp': datetime.now().isoformat()
            }
            
            # Check if articles data has changed
            has_changed = self.metadata_manager.has_entity_changed('articles', 'all', checksum_data)
            
            if has_changed:
                logger.info(f"Articles data has changed, storing updates")
                
                logger.info(f"Storing articles data at: {processed_data['path']}")
                self.store_data(
                    processed_data['path'],
                    json.dumps(processed_data['data'], indent=2),
                    'application/json'
                )
                
                # Update metadata
                article_count = len(processed_data['data']['articles']) if 'articles' in processed_data['data'] else 0
                logger.info(f"Updating metadata for {article_count} articles")
                
                self.metadata_manager.update_entity_checksum('articles', 'all', 
                                                          self.metadata_manager.calculate_checksum(checksum_data))
                
                # Also update last sync time
                self.metadata_manager.update_last_sync_time()
                
                logger.info(f"Successfully stored articles data")
            else:
                logger.info("Articles data has not changed, skipping storage")
            
        except Exception as e:
            logger.error(f"Error storing articles data: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    # Same pattern for clients
    def store_clients(self, processed_data: Dict) -> None:
        """
        Store clients data with change detection.
        
        Args:
            processed_data (Dict): Processed client data with paths and content
        """
        try:
            logger.info("Starting store_clients")
            
            # Create a checksum of the entire clients dataset
            checksum_data = {
                'count': len(processed_data['data']['clients']) if 'clients' in processed_data['data'] else 0,
                'timestamp': datetime.now().isoformat()
            }
            
            # Check if clients data has changed
            has_changed = self.metadata_manager.has_entity_changed('clients', 'all', checksum_data)
            
            if has_changed:
                logger.info(f"Clients data has changed, storing updates")
                
                logger.info(f"Storing clients data at: {processed_data['path']}")
                self.store_data(
                    processed_data['path'],
                    json.dumps(processed_data['data'], indent=2),
                    'application/json'
                )
                
                # Update metadata
                client_count = len(processed_data['data']['clients']) if 'clients' in processed_data['data'] else 0
                logger.info(f"Updating metadata for {client_count} clients")
                
                self.metadata_manager.update_entity_checksum('clients', 'all', 
                                                          self.metadata_manager.calculate_checksum(checksum_data))
                
                # Also update last sync time
                self.metadata_manager.update_last_sync_time()
                
                logger.info(f"Successfully stored clients data")
            else:
                logger.info("Clients data has not changed, skipping storage")
            
        except Exception as e:
            logger.error(f"Error storing clients data: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    # And for credit notes
    def store_credit_note(self, processed_data: Dict) -> None:
        """
        Store credit note data with change detection.
        
        Args:
            processed_data (Dict): Processed credit note data with paths and metadata
        """
        try:
            logger.info("Starting store_credit_note")
            
            # Extract key credit note data
            credit_note_id = processed_data['metadata'].get('id', 'unknown')
            credit_note_number = processed_data['metadata'].get('credit_note_number', 'unknown')
            
            # Check for changes using key properties
            checksum_data = {
                'status': processed_data['metadata'].get('status'),
                'total_amount': processed_data['metadata'].get('total_gross')
            }
            
            # Check if credit note has changed
            has_changed = self.metadata_manager.has_entity_changed('credit_note', credit_note_id, checksum_data)
            
            if has_changed:
                logger.info(f"Credit note {credit_note_number} (ID: {credit_note_id}) has changed or is new, storing updates")
                
                logger.info(f"Storing credit note data at: {processed_data['paths']['dataset']}")
                self.store_data(
                    processed_data['paths']['dataset'],
                    json.dumps(processed_data['metadata'], indent=2),
                    'application/json'
                )
                
                # Update metadata
                logger.info(f"Updating metadata for credit note: {credit_note_id}")
                self.metadata_manager.update_entity_checksum('credit_note', credit_note_id, 
                                                           self.metadata_manager.calculate_checksum(checksum_data))
                
                # Also update last sync time
                self.metadata_manager.update_last_sync_time()
                
                logger.info(f"Successfully stored credit note data")
            else:
                logger.info(f"Credit note {credit_note_number} (ID: {credit_note_id}) has not changed, skipping storage")
            
        except Exception as e:
            logger.error(f"Error storing credit note data: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def get_last_sync_time(self) -> Optional[str]:
        """
        Get the timestamp of the last successful sync
        
        Returns:
            ISO format timestamp or None if never synced
        """
        return self.metadata_manager.get_last_sync_time()
        
    def update_invoice_status_metadata(self, invoice_data: Dict) -> None:
        """
        Update the paid/unpaid invoice metadata files based on the invoice status.
        
        Args:
            invoice_data (Dict): The invoice metadata
        """
        try:
            logger.info("Updating invoice status metadata")
            
            # Extract key invoice info
            invoice_id = invoice_data.get('id', 'unknown')
            invoice_number = invoice_data.get('invoice_number', 'unknown')
            status = invoice_data.get('status', 'unknown').lower()  # Normalize status to lowercase
            total_gross = invoice_data.get('total_gross', '0')
            client_id = invoice_data.get('client_id', 'unknown')
            invoice_date = invoice_data.get('date', 'unknown')
            created_date = invoice_data.get('created', 'unknown')
            
            # Create invoice summary for metadata
            invoice_summary = {
                'id': invoice_id,
                'invoice_number': invoice_number,
                'status': status,
                'total_gross': total_gross,
                'client_id': client_id,
                'date': invoice_date,
                'created': created_date,
                'last_updated': datetime.now().isoformat()
            }
            
            # Log detailed info for debugging
            logger.info(f"Processing invoice {invoice_number} with status '{status}'")
            
            # Determine which metadata file to update based on status
            # Be explicit about what counts as "paid" status
            if status.lower() == 'paid':
                logger.info(f"Invoice {invoice_number} is paid, adding to paid metadata")
                # This is a paid invoice, add to paid metadata
                self._update_paid_invoices_metadata(invoice_summary)
                # Remove from unpaid if present
                self._remove_from_unpaid_invoices_metadata(invoice_id)
                logger.info(f"Invoice {invoice_number} added to paid metadata and removed from unpaid metadata")
            else:
                logger.info(f"Invoice {invoice_number} is not paid (status: {status}), adding to unpaid metadata")
                # This is an unpaid invoice (draft, open, overdue, etc.), add to unpaid metadata
                self._update_unpaid_invoices_metadata(invoice_summary)
                # Remove from paid if present
                self._remove_from_paid_invoices_metadata(invoice_id)
                logger.info(f"Invoice {invoice_number} added to unpaid metadata and removed from paid metadata")
                
            logger.info(f"Completed updating invoice status metadata for invoice {invoice_number} with status {status}")
            
        except Exception as e:
            logger.error(f"Error updating invoice status metadata: {str(e)}")
            logger.error(traceback.format_exc())
            # Don't raise to avoid interrupting main process
    
    def _update_paid_invoices_metadata(self, invoice_summary: Dict) -> None:
        """
        Update the paid invoices metadata file.
        
        Args:
            invoice_summary (Dict): Invoice summary data
        """
        try:
            # Get existing metadata or create new
            paid_invoices = self._get_metadata_file(self.paid_invoices_path)
            
            if not paid_invoices:
                # Initialize new metadata file structure
                paid_invoices = {
                    'last_updated': datetime.now().isoformat(),
                    'invoices': {}
                }
            elif 'invoices' not in paid_invoices:
                # Ensure invoices dict exists
                paid_invoices['invoices'] = {}
            
            # Update/add this invoice
            paid_invoices['invoices'][invoice_summary['id']] = invoice_summary
            paid_invoices['last_updated'] = datetime.now().isoformat()
            
            # Store updated metadata
            self.store_data(
                self.paid_invoices_path,
                json.dumps(paid_invoices, indent=2)
            )
            
            logger.info(f"Updated paid invoices metadata with invoice {invoice_summary['invoice_number']}")
            
        except Exception as e:
            logger.error(f"Error updating paid invoices metadata: {str(e)}")
            logger.error(traceback.format_exc())
    
    def _update_unpaid_invoices_metadata(self, invoice_summary: Dict) -> None:
        """
        Update the unpaid invoices metadata file.
        
        Args:
            invoice_summary (Dict): Invoice summary data
        """
        try:
            # Get existing metadata or create new
            unpaid_invoices = self._get_metadata_file(self.unpaid_invoices_path)
            
            if not unpaid_invoices:
                # Initialize new metadata file structure
                unpaid_invoices = {
                    'last_updated': datetime.now().isoformat(),
                    'invoices': {}
                }
            elif 'invoices' not in unpaid_invoices:
                # Ensure invoices dict exists
                unpaid_invoices['invoices'] = {}
            
            # Update/add this invoice
            unpaid_invoices['invoices'][invoice_summary['id']] = invoice_summary
            unpaid_invoices['last_updated'] = datetime.now().isoformat()
            
            # Store updated metadata
            self.store_data(
                self.unpaid_invoices_path,
                json.dumps(unpaid_invoices, indent=2)
            )
            
            logger.info(f"Updated unpaid invoices metadata with invoice {invoice_summary['invoice_number']}")
            
        except Exception as e:
            logger.error(f"Error updating unpaid invoices metadata: {str(e)}")
            logger.error(traceback.format_exc())
    
    def _remove_from_paid_invoices_metadata(self, invoice_id: str) -> None:
        """
        Remove an invoice from the paid invoices metadata file.
        
        Args:
            invoice_id (str): ID of the invoice to remove
        """
        try:
            # Get existing metadata
            paid_invoices = self._get_metadata_file(self.paid_invoices_path)
            
            if not paid_invoices or 'invoices' not in paid_invoices:
                # Nothing to remove
                return
            
            # Remove this invoice if present
            if invoice_id in paid_invoices['invoices']:
                logger.info(f"Removing invoice {invoice_id} from paid invoices metadata")
                invoice_number = paid_invoices['invoices'][invoice_id].get('invoice_number', 'unknown')
                del paid_invoices['invoices'][invoice_id]
                paid_invoices['last_updated'] = datetime.now().isoformat()
                
                # Store updated metadata
                self.store_data(
                    self.paid_invoices_path,
                    json.dumps(paid_invoices, indent=2)
                )
                
                logger.info(f"Removed invoice {invoice_number} (ID: {invoice_id}) from paid invoices metadata")
            
        except Exception as e:
            logger.error(f"Error removing from paid invoices metadata: {str(e)}")
            logger.error(traceback.format_exc())
    
    def _remove_from_unpaid_invoices_metadata(self, invoice_id: str) -> None:
        """
        Remove an invoice from the unpaid invoices metadata file.
        
        Args:
            invoice_id (str): ID of the invoice to remove
        """
        try:
            # Get existing metadata
            unpaid_invoices = self._get_metadata_file(self.unpaid_invoices_path)
            
            if not unpaid_invoices or 'invoices' not in unpaid_invoices:
                # Nothing to remove
                return
            
            # Remove this invoice if present
            if invoice_id in unpaid_invoices['invoices']:
                logger.info(f"Removing invoice {invoice_id} from unpaid invoices metadata")
                invoice_number = unpaid_invoices['invoices'][invoice_id].get('invoice_number', 'unknown')
                del unpaid_invoices['invoices'][invoice_id]
                unpaid_invoices['last_updated'] = datetime.now().isoformat()
                
                # Store updated metadata
                self.store_data(
                    self.unpaid_invoices_path,
                    json.dumps(unpaid_invoices, indent=2)
                )
                
                logger.info(f"Removed invoice {invoice_number} (ID: {invoice_id}) from unpaid invoices metadata")
            
        except Exception as e:
            logger.error(f"Error removing from unpaid invoices metadata: {str(e)}")
            logger.error(traceback.format_exc())
    
    def _get_metadata_file(self, path: str) -> Optional[Dict]:
        """
        Get the contents of a metadata file.
        
        Args:
            path (str): Path to the metadata file
            
        Returns:
            Dict or None: Contents of the metadata file, or None if not found
        """
        try:
            data = self.get_data(path)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.warning(f"Error reading metadata file {path}: {str(e)}")
            return None
            
    def check_month_end_invoice_updates(self, client) -> None:
        """
        Monthly check to update invoice status changes.
        This should be run on the last day of each month to check for status changes.
        
        Args:
            client: BillomatClient instance for API access
        """
        try:
            logger.info("Starting month-end invoice status check")
            
            # Double-check if today is the last day of the month
            today = datetime.now().date()
            _, last_day = calendar.monthrange(today.year, today.month)
            
            if today.day != last_day:
                logger.info(f"Today ({today}) is not the last day of the month. Skipping month-end check.")
                return
            
            # Get unpaid invoices metadata
            unpaid_invoices = self._get_metadata_file(self.unpaid_invoices_path)
            
            if not unpaid_invoices or 'invoices' not in unpaid_invoices or not unpaid_invoices['invoices']:
                logger.info("No unpaid invoices to check.")
                return
            
            logger.info(f"Checking {len(unpaid_invoices['invoices'])} unpaid invoices for status changes")
            
            # Track invoices that need to be updated
            invoices_to_update = []
            
            # Check each unpaid invoice for status changes
            for invoice_id, invoice_data in unpaid_invoices['invoices'].items():
                try:
                    # Get latest invoice data from API
                    current_invoice_xml = client.get_invoice(invoice_id)
                    
                    # Process invoice data
                    from src.data_pipelines.billomat.processor import BillomatDataProcessor
                    processor = BillomatDataProcessor()
                    
                    # Convert XML to dictionary
                    current_invoice_data = BillomatDataProcessor.xml_to_dict(current_invoice_xml)
                    
                    # Get current status (case-insensitive comparison)
                    current_status = current_invoice_data.get('status', '').lower()
                    stored_status = invoice_data.get('status', '').lower()
                    
                    # Check for changes in status, total amount, or other key fields
                    has_changed = (
                        current_status != stored_status or
                        current_invoice_data.get('total_gross') != invoice_data.get('total_gross')
                    )
                    
                    if has_changed:
                        logger.info(f"Invoice {invoice_data['invoice_number']} has changed status from '{stored_status}' to '{current_status}'. Adding to update list.")
                        
                        # Get PDF if available
                        pdf_data = None
                        try:
                            pdf_data = client.get_invoice_pdf(invoice_id)
                        except Exception as pdf_err:
                            logger.warning(f"Could not retrieve PDF for invoice {invoice_id}: {str(pdf_err)}")
                        
                        # Process invoice data for storage
                        processed_data = processor.process_invoice(current_invoice_xml, 
                                                              pdf_metadata=pdf_data.get('metadata') if pdf_data else None)
                        
                        # Add to update list
                        invoices_to_update.append((processed_data, pdf_data))
                        
                except Exception as e:
                    logger.error(f"Error checking invoice {invoice_id}: {str(e)}")
                    logger.error(traceback.format_exc())
                    continue
            
            # Update invoices with changes
            logger.info(f"Found {len(invoices_to_update)} invoices with changes to update")
            
            for processed_data, pdf_data in invoices_to_update:
                try:
                    # Store updated invoice data
                    self.store_invoice_complete(processed_data, pdf_data)
                    logger.info(f"Successfully updated invoice {processed_data['metadata'].get('invoice_number')}")
                except Exception as e:
                    logger.error(f"Error updating invoice {processed_data['metadata'].get('id')}: {str(e)}")
                    logger.error(traceback.format_exc())
            
            logger.info("Completed month-end invoice status check")
            
        except Exception as e:
            logger.error(f"Error during month-end invoice check: {str(e)}")
            logger.error(traceback.format_exc())