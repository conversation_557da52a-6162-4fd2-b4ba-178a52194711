"""
Manages all Google Cloud Storage (GCS) operations
Main responsibilities:

    Stores data in the GCS bucket
    Handles different types of content (JSON, PDFs)
    Prevents duplicate storage
    Organizes files in year/month structure
    Stores both invoice data and their PDFs
    Converts PDFs to base64 for storage
"""
from typing import Any, Dict, Optional
from google.cloud import storage
from google.cloud.storage.blob import Blob
import json
import logging
from src.infrastructure.gcs.client import GCSClient
from src.infrastructure.monitoring.logging import setup_logger

logger = setup_logger(__name__)

class GCSStorageManager:
    """Manages storage operations for Google Cloud Storage."""
    
    def __init__(self, gcs_client: Optional[GCSClient] = None):
        """
        Initialize the storage manager with a GCS client.
        
        Args:
            gcs_client (Optional[GCSClient]): GCS client instance. If None, creates new instance.
        """
        self.client = gcs_client or GCSClient()
        self.bucket = self.client.get_bucket()

    def store_data(self, blob_path: str, content: Any, content_type: str = 'application/json') -> None:
        """
        Store data in GCS with duplicate prevention.
        
        Args:
            blob_path (str): Path where the blob should be stored
            content (Any): Content to store
            content_type (str): Content type of the data
        """
        try:
            blob = self.bucket.blob(blob_path)
            
            # Check if blob exists
            if blob.exists():
                logger.info(f"Updating existing file at {blob_path}")
                blob.delete()
            
            # Upload new content
            blob.upload_from_string(content, content_type=content_type)
            logger.info(f"Successfully stored data at {blob_path}")
            
        except Exception as e:
            logger.error(f"Error storing data: {str(e)}")
            raise

    def store_invoice_complete(self, processed_data: Dict, pdf_data: Optional[Dict] = None) -> None:
        """
        Store all invoice-related data.
        
        Args:
            processed_data (Dict): Processed invoice data with paths and metadata
            pdf_data (Optional[Dict]): PDF data including base64 content and metadata
        """
        # Store invoice dataset
        self.store_data(
            processed_data['paths']['dataset'],
            json.dumps(processed_data['metadata'], indent=2)
        )
        
        # Store PDF if available
        if pdf_data and 'pdf_base64' in pdf_data:
            # Store base64 encoded PDF
            self.store_data(
                processed_data['paths']['pdf'],
                pdf_data['pdf_base64'],
                'text/plain'  # Base64 stored as text
            )
            
            # Store PDF metadata
            if 'metadata' in pdf_data:
                self.store_data(
                    processed_data['paths']['pdf_metadata'],
                    json.dumps(pdf_data['metadata'], indent=2)
                )

    def store_articles(self, processed_data: Dict) -> None:
        """
        Store articles data as JSON.
        
        Args:
            processed_data (Dict): Processed articles data with path and content
        """
        try:
            self.store_data(
                processed_data['path'],
                json.dumps(processed_data['data'], indent=2),
                'application/json'
            )
            logger.info(f"Successfully stored articles data at {processed_data['path']}")
        except Exception as e:
            logger.error(f"Error storing articles data: {str(e)}")
            raise

    def store_clients(self, processed_data: Dict) -> None:
        """
        Store clients data with error handling.
        
        Args:
            processed_data (Dict): Processed client data with paths and content
        """
        try:
            self.store_data(
                processed_data['path'],
                json.dumps(processed_data['data'], indent=2),
                'application/json'
            )
            logger.info(f"Successfully stored clients data at {processed_data['path']}")
        except Exception as e:
            logger.error(f"Error storing clients data: {str(e)}")
            raise

    def store_credit_note(self, processed_data: Dict) -> None:
        """
        Store credit note data with error handling.
        
        Args:
            processed_data (Dict): Processed credit note data with paths and metadata
        """
        try:
            self.store_data(
                processed_data['paths']['dataset'],
                json.dumps(processed_data['metadata'], indent=2),
                'application/json'
            )
            logger.info(f"Successfully stored credit note data at {processed_data['paths']['dataset']}")
        except Exception as e:
            logger.error(f"Error storing credit note data: {str(e)}")
            raise