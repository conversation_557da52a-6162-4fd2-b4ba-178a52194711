

"""This is with enhanced version of the code block with updated error logging and fallbacks for better approach """
from google.cloud import storage
from google.cloud.storage.bucket import Bucket
from google.auth.exceptions import DefaultCredentialsError
import os
import traceback
from typing import Optional
from src.infrastructure.monitoring.logging import setup_logger
import yaml
from pathlib import Path

logger = setup_logger(__name__)

class GCSClient:
    """
    Client for interacting with Google Cloud Storage.
    Handles authentication, connection management, and basic bucket operations.
    """
    
    def __init__(
        self,
        bucket_name: Optional[str] = None,
        project_id: Optional[str] = None,
        credentials_path: Optional[str] = None
    ):
        """
        Initialize the GCS client with configuration parameters.
        
        Args:
            bucket_name (Optional[str]): Name of the GCS bucket
            project_id (Optional[str]): Google Cloud project ID
            credentials_path (Optional[str]): Path to service account credentials file
        """
        logger.info("Initializing GCSClient")
        try:
            # Log environment variables (excluding sensitive ones)
            logger.info("=== ENVIRONMENT VARIABLES IN GCS CLIENT ===")
            for key, value in os.environ.items():
                if 'KEY' not in key.upper() and 'SECRET' not in key.upper() and 'PASSWORD' not in key.upper():
                    logger.info(f"{key}: {value}")
                else:
                    logger.info(f"{key}: [REDACTED]")
                    
            self.bucket_name = bucket_name or self._get_bucket_name()
            logger.info(f"Using bucket name: {self.bucket_name}")
            
            self.project_id = project_id or self._get_project_id()
            logger.info(f"Using project ID: {self.project_id}")
            
            self.credentials_path = credentials_path
            if credentials_path:
                logger.info(f"Using credentials from: {credentials_path}")
            else:
                logger.info("Using default credentials")
            
            # Initialize client and bucket
            self.client = self._initialize_client()
            logger.info("GCS client initialized successfully")
            
            self._bucket: Optional[Bucket] = None
            
        except Exception as e:
            logger.error(f"Error initializing GCSClient: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def _get_config(self) -> dict:
        """
        Load configuration from yaml file based on environment.
        
        Returns:
            dict: Configuration dictionary
        """
        try:
            env = os.getenv('ENVIRONMENT', 'development')
            logger.info(f"Loading configuration for environment: {env}")
            
            config_path = Path(__file__).parents[3] / 'configs' / f'{env}.yaml'
            logger.info(f"Looking for config file at: {config_path}")
            
            if not config_path.exists():
                logger.warning(f"Config file not found at {config_path}")
                return {}
                
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                logger.info(f"Loaded config keys: {list(config.keys())}")
                return config.get('gcs', {})
                
        except Exception as e:
            logger.warning(f"Failed to load config file: {str(e)}. Using environment variables.")
            logger.warning(traceback.format_exc())
            return {}

    def _get_bucket_name(self) -> str:
        """
        Get bucket name from config or environment variables.
        
        Returns:
            str: GCS bucket name
        
        Raises:
            ValueError: If bucket name is not configured
        """
        # First try from environment variable with direct access
        env_bucket = os.environ.get('GCS_BUCKET_NAME')
        if env_bucket:
            logger.info(f"Found bucket name in environment variable: {env_bucket}")
            return env_bucket
            
        # Then try through getenv (redundant but thorough)
        env_bucket = os.getenv('GCS_BUCKET_NAME')
        if env_bucket:
            logger.info(f"Found bucket name through getenv: {env_bucket}")
            return env_bucket
            
        # Finally try from config
        config_bucket = self._get_config().get('bucket_name')
        if config_bucket:
            logger.info(f"Found bucket name in config: {config_bucket}")
            return config_bucket
            
        # If we get here, we don't have a bucket name - use hardcoded fallback
        fallback_bucket = "raw-data-external-source-437915-1"
        logger.warning(f"GCS bucket name not configured, using fallback: {fallback_bucket}")
        return fallback_bucket

    def _get_project_id(self) -> str:
        """
        Get project ID from config or environment variables.
        
        Returns:
            str: Google Cloud project ID
        
        Raises:
            ValueError: If project ID is not configured
        """
        # First try environment variable
        project_id = os.getenv('GCP_PROJECT')
        if project_id:
            logger.info(f"Found project ID in environment variable: {project_id}")
            return project_id
            
        # Then try from config
        config_project = self._get_config().get('project_id')
        if config_project:
            logger.info(f"Found project ID in config: {config_project}")
            return config_project
            
        # Fallback to a default if neither is available
        fallback_project = "external-data-source-437915"
        logger.warning(f"GCS project ID not configured, using fallback: {fallback_project}")
        return fallback_project

    def _initialize_client(self) -> storage.Client:
        """
        Initialize and configure the GCS client.
        
        Returns:
            storage.Client: Configured GCS client
        
        Raises:
            Exception: If client initialization fails
        """
        try:
            logger.info("Initializing GCS client")
            
            # If credentials path is provided, use it
            if self.credentials_path:
                logger.info(f"Creating client with service account: {self.credentials_path}")
                return storage.Client.from_service_account_json(
                    self.credentials_path,
                    project=self.project_id
                )
            
            # Otherwise, try default credentials
            logger.info("Creating client with default credentials")
            client = storage.Client(project=self.project_id)
            logger.info("GCS client initialization successful")
            return client
            
        except DefaultCredentialsError as e:
            logger.error(f"Failed to initialize GCS client with default credentials: {str(e)}")
            logger.error(traceback.format_exc())
            raise
        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def get_bucket(self) -> Bucket:
        """
        Get or create the GCS bucket instance.
        
        Returns:
            Bucket: GCS bucket instance
        
        Raises:
            Exception: If bucket access fails
        """
        if self._bucket is None:
            try:
                logger.info(f"Getting bucket: {self.bucket_name}")
                self._bucket = self.client.bucket(self.bucket_name)
                
                # Verify bucket access
                logger.info("Checking if bucket exists")
                if not self._bucket.exists():
                    logger.warning(f"Bucket {self.bucket_name} does not exist")
                    if self._should_create_bucket():
                        logger.info("Auto-create is enabled, creating bucket")
                        self._bucket = self.create_bucket()
                    else:
                        error_msg = f"Bucket {self.bucket_name} does not exist and auto-creation is disabled"
                        logger.error(error_msg)
                        raise ValueError(error_msg)
                else:
                    logger.info(f"Successfully accessed bucket: {self.bucket_name}")
                        
            except Exception as e:
                logger.error(f"Error accessing bucket {self.bucket_name}: {str(e)}")
                logger.error(traceback.format_exc())
                raise
                
        return self._bucket

    def _should_create_bucket(self) -> bool:
        """
        Check if bucket should be auto-created based on configuration.
        
        Returns:
            bool: True if bucket should be created, False otherwise
        """
        auto_create = self._get_config().get('auto_create_bucket', False)
        logger.info(f"Auto-create bucket setting: {auto_create}")
        return auto_create

    def create_bucket(self) -> Bucket:
        """
        Create a new GCS bucket.
        
        Returns:
            Bucket: Newly created bucket instance
        
        Raises:
            Exception: If bucket creation fails
        """
        try:
            logger.info(f"Creating bucket {self.bucket_name}")
            bucket = self.client.create_bucket(self.bucket_name)
            logger.info(f"Successfully created bucket {self.bucket_name}")
            return bucket
        except Exception as e:
            logger.error(f"Failed to create bucket {self.bucket_name}: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def list_blobs(self, prefix: Optional[str] = None) -> list:
        """
        List all blobs in the bucket with optional prefix filtering.
        
        Args:
            prefix (Optional[str]): Filter blobs by prefix
            
        Returns:
            list: List of blob names
        """
        try:
            logger.info(f"Listing blobs with prefix: {prefix or 'None'}")
            blobs = self.client.list_blobs(self.bucket_name, prefix=prefix)
            blob_names = [blob.name for blob in blobs]
            logger.info(f"Found {len(blob_names)} blobs")
            return blob_names
        except Exception as e:
            logger.error(f"Error listing blobs with prefix {prefix}: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def delete_blob(self, blob_name: str) -> None:
        """
        Delete a blob from the bucket.
        
        Args:
            blob_name (str): Name of the blob to delete
        """
        try:
            logger.info(f"Deleting blob: {blob_name}")
            blob = self.get_bucket().blob(blob_name)
            if blob.exists():
                blob.delete()
                logger.info(f"Successfully deleted blob {blob_name}")
            else:
                logger.warning(f"Blob {blob_name} does not exist")
        except Exception as e:
            logger.error(f"Error deleting blob {blob_name}: {str(e)}")
            logger.error(traceback.format_exc())
            raise