"""
Handles storage and retrieval of metadata for tracking sync state
"""
import json
import logging
import hashlib
from datetime import datetime
from typing import Dict, Any, Optional, List

from src.infrastructure.gcs.base_storage import BaseGCSStorageManager
from src.infrastructure.monitoring.logging import setup_logger

logger = setup_logger(__name__)

class MetadataManager:
    """
    Manages metadata for tracking sync state of external data sources
    """
    
    def __init__(self, storage_manager: BaseGCSStorageManager, data_source: str):
        """
        Initialize the metadata manager
        
        Args:
            storage_manager: Storage manager to use for metadata storage
            data_source: Data source identifier (e.g., 'billomat')
        """
        self.storage_manager = storage_manager
        self.data_source = data_source
        self.metadata_path = f"{data_source}/metadata/sync_state.json"
        self._metadata = None
    
    def _load_metadata(self) -> Dict[str, Any]:
        """
        Load metadata from storage or create initial structure if not exists
        
        Returns:
            Dict containing the metadata
        """
        if self._metadata is not None:
            return self._metadata
            
        try:
            data = self.storage_manager.get_data(self.metadata_path)
            if data:
                self._metadata = json.loads(data)
                logger.info(f"Loaded metadata for {self.data_source}")
                return self._metadata
        except Exception as e:
            logger.warning(f"Error loading metadata: {str(e)}")
        
        # Create initial metadata structure if not exists
        self._metadata = {
            "last_sync": None,
            "entities": {}
        }
        
        return self._metadata
    
    def _save_metadata(self):
        """Save current metadata to storage"""
        try:
            self.storage_manager.store_data(
                self.metadata_path,
                json.dumps(self._metadata, indent=2)
            )
            logger.info(f"Saved metadata for {self.data_source}")
        except Exception as e:
            logger.error(f"Error saving metadata: {str(e)}")
            raise
    
    def get_last_sync_time(self) -> Optional[str]:
        """
        Get the timestamp of the last successful sync
        
        Returns:
            ISO format timestamp or None if never synced
        """
        metadata = self._load_metadata()
        return metadata.get("last_sync")
    
    def update_last_sync_time(self):
        """Update the last sync timestamp to current time"""
        metadata = self._load_metadata()
        metadata["last_sync"] = datetime.now().isoformat()
        self._save_metadata()
        logger.info(f"Updated last sync time for {self.data_source}")
    
    def get_entity_checksum(self, entity_type: str, entity_id: str) -> Optional[str]:
        """
        Get stored checksum for an entity
        
        Args:
            entity_type: Type of entity (e.g., 'invoice', 'client')
            entity_id: Unique identifier of the entity
            
        Returns:
            Stored checksum or None if entity not previously processed
        """
        metadata = self._load_metadata()
        return metadata.get("entities", {}).get(entity_type, {}).get(entity_id)
    
    def update_entity_checksum(self, entity_type: str, entity_id: str, checksum: str):
        """
        Update checksum for an entity
        
        Args:
            entity_type: Type of entity (e.g., 'invoice', 'client') 
            entity_id: Unique identifier of the entity
            checksum: New checksum value
        """
        metadata = self._load_metadata()
        
        # Ensure entity type dict exists
        if entity_type not in metadata["entities"]:
            metadata["entities"][entity_type] = {}
        
        # Update checksum
        metadata["entities"][entity_type][entity_id] = checksum
        
        # Save changes
        self._save_metadata()
    
    def has_entity_changed(self, entity_type: str, entity_id: str, entity_data: Dict[str, Any]) -> bool:
        """
        Check if an entity has changed based on its checksum
        
        Args:
            entity_type: Type of entity (e.g., 'invoice', 'client')
            entity_id: Unique identifier of the entity
            entity_data: Data to calculate checksum from
            
        Returns:
            True if entity has changed or is new, False otherwise
        """
        # Calculate new checksum
        new_checksum = self.calculate_checksum(entity_data)
        
        # Get stored checksum
        stored_checksum = self.get_entity_checksum(entity_type, entity_id)
        
        # If no stored checksum, entity is new
        if stored_checksum is None:
            return True
        
        # Compare checksums
        return new_checksum != stored_checksum
    
    @staticmethod
    def calculate_checksum(data: Dict[str, Any]) -> str:
        """
        Calculate checksum for entity data
        
        Args:
            data: Entity data to calculate checksum from
            
        Returns:
            Checksum string
        """
        # Convert data to string and calculate hash
        data_str = json.dumps(data, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def clear_metadata(self):
        """Clear all metadata (use with caution)"""
        self._metadata = {
            "last_sync": None,
            "entities": {}
        }
        self._save_metadata()
        logger.info(f"Cleared metadata for {self.data_source}")