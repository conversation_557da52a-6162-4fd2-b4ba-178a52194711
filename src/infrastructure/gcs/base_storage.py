"""
Base Storage Manager for Google Cloud Storage operations
Handles core storage functionality used across all data sources
"""
from typing import Any, Dict, Optional
from google.cloud import storage
from google.cloud.storage.blob import Blob
import json
import logging
from datetime import datetime
from src.infrastructure.gcs.client import GCSClient
from src.infrastructure.monitoring.logging import setup_logger

logger = setup_logger(__name__)


class BaseGCSStorageManager:
    """Base class for Google Cloud Storage operations."""
    
    def __init__(self, gcs_client: Optional[GCSClient] = None):
        """
        Initialize the storage manager with a GCS client.
        
        Args:
            gcs_client (Optional[GCSClient]): GCS client instance. If None, creates new instance.
        """
        self.client = gcs_client or GCSClient()
        self.bucket = self.client.get_bucket()

    def store_data(self, blob_path: str, content: Any, content_type: str = 'application/json') -> None:
        """
        Store data in GCS with duplicate prevention.
        
        Args:
            blob_path (str): Path where the blob should be stored
            content (Any): Content to store
            content_type (str): Content type of the data
        """
        try:
            blob = self.bucket.blob(blob_path)
            
            # Check if blob exists
            if blob.exists():
                logger.info(f"Updating existing file at {blob_path}")
                blob.delete()
            
            # Upload new content
            blob.upload_from_string(content, content_type=content_type)
            logger.info(f"Successfully stored data at {blob_path}")
            
        except Exception as e:
            logger.error(f"Error storing data: {str(e)}")
            raise
    
    def get_data(self, blob_path: str) -> Optional[str]:
        """
        Retrieve data from GCS.
        
        Args:
            blob_path (str): Path to the blob
            
        Returns:
            Optional[str]: Content of the blob or None if not found
        """
        try:
            blob = self.bucket.blob(blob_path)
            if blob.exists():
                return blob.download_as_text()
            else:
                logger.warning(f"Blob {blob_path} not found")
                return None
        except Exception as e:
            logger.error(f"Error retrieving data from {blob_path}: {str(e)}")
            raise
    
    def delete_data(self, blob_path: str) -> bool:
        """
        Delete data from GCS.
        
        Args:
            blob_path (str): Path to the blob
            
        Returns:
            bool: True if deleted, False if not found
        """
        try:
            blob = self.bucket.blob(blob_path)
            if blob.exists():
                blob.delete()
                logger.info(f"Successfully deleted data at {blob_path}")
                return True
            else:
                logger.warning(f"Cannot delete: Blob {blob_path} not found")
                return False
        except Exception as e:
            logger.error(f"Error deleting data at {blob_path}: {str(e)}")
            raise
    
    def list_blobs(self, prefix: str) -> list:
        """
        List all blobs with a given prefix.
        
        Args:
            prefix (str): Prefix to filter blobs
            
        Returns:
            list: List of blob names
        """
        try:
            return self.client.list_blobs(prefix=prefix)
        except Exception as e:
            logger.error(f"Error listing blobs with prefix {prefix}: {str(e)}")
            raise
    
    def update_metadata(self, metadata_path: str, data_type: str, data_id: Optional[str] = None,
                      additional_info: Optional[Dict] = None) -> None:
        """
        Update metadata file to track operations.
        
        Args:
            metadata_path (str): Path to metadata file
            data_type (str): Type of data being updated
            data_id (Optional[str]): ID of the updated data
            additional_info (Optional[Dict]): Additional information to store
        """
        try:
            # Get current datetime
            current_time = datetime.now().isoformat()
            
            # Try to read existing metadata
            try:
                blob = self.bucket.blob(metadata_path)
                if blob.exists():
                    metadata_content = blob.download_as_text()
                    metadata = json.loads(metadata_content)
                else:
                    metadata = {
                        "last_update": current_time,
                        "updates": []
                    }
            except Exception:
                # If file doesn't exist or can't be read, create new
                metadata = {
                    "last_update": current_time,
                    "updates": []
                }
            
            # Prepare update details
            update_info = {
                "type": data_type,
                "id": data_id,
                "timestamp": current_time
            }
            
            # Add additional info if provided
            if additional_info:
                update_info.update(additional_info)
            
            # Add to updates list
            metadata["updates"].append(update_info)
            
            # Update last_update timestamp
            metadata["last_update"] = current_time
            
            # Store updated metadata
            self.store_data(metadata_path, json.dumps(metadata, indent=2))
            
            logger.info(f"Updated metadata at {metadata_path} for {data_type}")
            
        except Exception as e:
            logger.error(f"Error updating metadata at {metadata_path}: {str(e)}")
            # No raise here to prevent interrupting the main operation