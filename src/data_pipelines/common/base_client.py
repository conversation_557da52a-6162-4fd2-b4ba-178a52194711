from abc import ABC, abstractmethod
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Optional, Dict, Any
import time
import logging

class BaseAPIClient(ABC):
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.session = self._create_session()
        self.logger = logging.getLogger(__name__)

    def _create_session(self) -> requests.Session:
        """Create a session with enhanced retry strategy."""
        session = requests.Session()
        
        retry_strategy = Retry(
            total=5,  # Increased from 3 to 5
            backoff_factor=2,  # Increased from 1 to 2
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "TRACE"],
            respect_retry_after_header=True
        )
        
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=3,  # Increased pool size
            pool_maxsize=10  # Increased pool maxsize
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session

    @abstractmethod
    def _get_headers(self) -> Dict[str, str]:
        pass

    @abstractmethod
    def _get_base_url(self) -> str:
        pass

    def _make_request(
        self, 
        endpoint: str, 
        method: str = 'GET', 
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        timeout: int = 60,  # Increased default timeout
        max_attempts: int = 3
    ) -> requests.Response:
        """Make API request with enhanced error handling and retry logic."""
        url = f"{self.base_url}{endpoint}"
        current_attempt = 0
        
        while current_attempt < max_attempts:
            try:
                self.logger.debug(f"Attempting request to {url} (Attempt {current_attempt + 1}/{max_attempts})")
                
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=self._get_headers(),
                    params=params,
                    json=data,
                    timeout=(10, timeout)  # (connect timeout, read timeout)
                )
                
                response.raise_for_status()
                return response
                
            except requests.exceptions.ReadTimeout as e:
                current_attempt += 1
                if current_attempt < max_attempts:
                    wait_time = (2 ** current_attempt) * 5  # Exponential backoff
                    self.logger.warning(f"Request timed out. Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                else:
                    raise Exception(f"API request failed after {max_attempts} attempts: {str(e)}")
                    
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:
                    retry_after = int(e.response.headers.get('Retry-After', 60))
                    self.logger.warning(f"Rate limited. Waiting {retry_after} seconds...")
                    time.sleep(retry_after)
                    continue
                raise Exception(f"HTTP error occurred: {str(e)}")
                
            except requests.exceptions.RequestException as e:
                current_attempt += 1
                if current_attempt < max_attempts:
                    wait_time = (2 ** current_attempt) * 5
                    self.logger.warning(f"Request failed. Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                else:
                    raise Exception(f"API request failed after {max_attempts} attempts: {str(e)}")