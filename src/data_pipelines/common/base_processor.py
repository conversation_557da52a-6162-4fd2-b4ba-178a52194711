from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from datetime import datetime

class BaseDataProcessor(ABC):
    @abstractmethod
    def process_data(self, data: Any) -> Dict[str, Any]:
        """Process raw data into structured format."""
        pass

    def generate_file_path(
        self, 
        base_path: str,
        entity_type: str,
        date: datetime,
        identifier: str,
        extension: str = 'json'
    ) -> str:
        """Generate standardized file path for storing data."""
        year = date.strftime('%Y')
        month = date.strftime('%m')
        timestamp = date.strftime('%Y%m%d_%H%M%S')
        
        return f"{base_path}/{entity_type}/{year}/{month}/{entity_type}_{identifier}_{timestamp}.{extension}"

    def clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and standardize data."""
        cleaned = {}
        for key, value in data.items():
            if isinstance(value, str):
                cleaned[key] = value.strip()
            elif value is None:
                continue
            else:
                cleaned[key] = value
        return cleaned

    def add_metadata(
        self, 
        data: Dict[str, Any],
        entity_type: str,
        source: str = 'billomat'
    ) -> Dict[str, Any]:
        """Add standard metadata to processed data."""
        return {
            'data': data,
            'metadata': {
                'processed_at': datetime.utcnow().isoformat(),
                'source': source,
                'entity_type': entity_type,
                'version': '1.0'
            }
        }