# """
# Processes and transforms the raw data
# Key functions:

#     Converts XML responses to dictionaries
#     Structures data for storage
#     Creates folder paths based on dates
#     Processes invoice metadata
#     Handles file naming conventions
#     Prepares data in the correct format for storage
# """

# import xml.etree.ElementTree as ET
# import json
# import csv
# from datetime import datetime, timezone
# import logging
# from io import StringIO

# from src.data_pipelines.common.base_processor import BaseDataProcessor
# from src.infrastructure.monitoring.logging import setup_logger

# logger = setup_logger(__name__)

# # logger = logging.getLogger(__name__)

# class BillomatDataProcessor(BaseDataProcessor):
#     def process_data(self, data, data_type: str = None, **kwargs):
#         """
#         Implementation of the abstract process_data method from BaseDataProcessor.

#         Args:
#             data: The data to process (XML content)
#             data_type: Type of data ('invoice', 'credit_note', 'article', 'client')
#             **kwargs: Additional arguments specific to each data type

#         Returns:
#             dict: Processed data with appropriate structure
#         """
#         try:
#             if data_type == 'invoice':
#                 pdf_metadata = kwargs.get('pdf_metadata')
#                 return self.process_invoice(data, pdf_metadata)
#             elif data_type == 'credit_note':
#                 return self.process_credit_note(data)
#             elif data_type == 'article':
#                 return self.process_articles(data,
#                                           from_date=kwargs.get('from_date'),
#                                           to_date=kwargs.get('to_date'))
#             elif data_type == 'client':
#                 return self.process_clients(data,
#                                          from_date=kwargs.get('from_date'),
#                                          to_date=kwargs.get('to_date'))
#             else:
#                 raise ValueError(f"Unsupported data type: {data_type}")

#         except Exception as e:
#             logger.error(f"Error processing {data_type} data: {str(e)}")
#             raise

#     @staticmethod
#     def process_invoice(invoice_xml, pdf_metadata = None):
#         """Process invoice data and prepare it for storage."""
#         # Existing implementation remains the same
#         metadata = BillomatDataProcessor.xml_to_dict(invoice_xml)

#         # Parse invoice date for folder structure
#         invoice_date = datetime.strptime(metadata['date'], '%Y-%m-%d')
#         year = invoice_date.strftime('%Y')
#         month = invoice_date.strftime('%m')

#         # Parse created timestamp for filename
#         created_date = datetime.strptime(metadata['created'], '%Y-%m-%dT%H:%M:%S%z')
#         timestamp = created_date.strftime('%Y%m%d_%H%M%S')

#         invoice_id = metadata['id']
#         invoice_number = metadata['invoice_number']

#         if pdf_metadata:
#             metadata['pdf_metadata'] = pdf_metadata

#         paths = {
#             'dataset': f"cbtouristenkarten-billomat/datasets/invoices/{year}/{month}/invoice_{invoice_id}_{timestamp}_{invoice_number}.json",
#             'pdf': f"cbtouristenkarten-billomat/invoices/{year}/{month}/invoice_{invoice_number}.pdf",
#             'pdf_metadata': f"cbtouristenkarten-billomat/invoices/{year}/{month}/invoice_{invoice_number}_metadata.json"
#         }

#         return {
#             'metadata': metadata,
#             'paths': paths,
#             'year': year,
#             'month': month,
#             'id': invoice_id
#         }

#     @staticmethod
#     def process_articles(articles_xml, from_date=None, to_date=None):
#         """
#         Process articles data and convert to JSON with labels.
#         Only stores articles created within the specified date range if provided.

#         Args:
#             articles_xml: XML content of articles
#             from_date: Filter articles created on or after this date (YYYY-MM-DD)
#             to_date: Filter articles created on or before this date (YYYY-MM-DD)

#         Returns:
#             Dict with processed data and storage path
#         """
#         try:
#             root = ET.fromstring(articles_xml)
#             articles_list = []
#             filtered_articles = []

#             for article in root.findall('.//article'):
#                 article_data = {}

#                 # Extract all fields with their types
#                 for field in article:
#                     field_type = field.get('type')
#                     field_value = field.text

#                     # Convert values based on type
#                     if field_type == 'integer':
#                         article_data[field.tag] = int(field_value) if field_value else None
#                     elif field_type == 'float':
#                         article_data[field.tag] = float(field_value) if field_value else None
#                     elif field_type == 'datetime':
#                         article_data[field.tag] = field_value  # Keep datetime as string
#                     else:
#                         article_data[field.tag] = field_value

#                 articles_list.append(article_data)

#             # Filter articles by creation date if date range is provided
#             if from_date or to_date:
#                 logger.info(f"Filtering articles by date range: {from_date} to {to_date}")

#                 # Convert string dates to datetime objects for comparison
#                 from_date_obj = None
#                 to_date_obj = None

#                 if from_date:
#                     if isinstance(from_date, str):
#                         from_date_obj = datetime.fromisoformat(from_date.replace('Z', '+00:00'))
#                     else:
#                         from_date_obj = from_date

#                 if to_date:
#                     if isinstance(to_date, str):
#                         to_date_obj = datetime.fromisoformat(to_date.replace('Z', '+00:00'))
#                     else:
#                         to_date_obj = to_date

#                     # If to_date doesn't have time component, set it to end of day
#                     if to_date_obj.hour == 0 and to_date_obj.minute == 0 and to_date_obj.second == 0:
#                         to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)

#                 # Filter articles by created date
#                 for article in articles_list:
#                     if 'created' in article:
#                         created_date = datetime.fromisoformat(article['created'].replace('Z', '+00:00'))

#                         # Check if article is within date range
#                         if from_date_obj and created_date < from_date_obj:
#                             continue
#                         if to_date_obj and created_date > to_date_obj:
#                             continue

#                         filtered_articles.append(article)

#                 logger.info(f"Filtered {len(filtered_articles)} articles out of {len(articles_list)}")
#             else:
#                 # No filtering, use all articles
#                 filtered_articles = articles_list

#             # Create structured output with metadata
#             current_date = datetime.now()
#             output_data = {
#                 'metadata': {
#                     'timestamp': current_date.isoformat(),
#                     'total_articles': len(filtered_articles),
#                     'data_format': 'JSON',
#                     'from_date': from_date.isoformat() if from_date and hasattr(from_date, 'isoformat') else from_date,
#                     'to_date': to_date.isoformat() if to_date and hasattr(to_date, 'isoformat') else to_date
#                 },
#                 'articles': filtered_articles
#             }

#             filename = f"articles_{current_date.strftime('%Y_%m_%d')}.json"
#             path = f"cbtouristenkarten-billomat/datasets/articles/{filename}"

#             return {
#                 'data': output_data,
#                 'path': path
#             }
#         except Exception as e:
#             logger.error(f"Error processing articles: {str(e)}")
#             raise

#     @staticmethod
#     def process_clients(clients_xml, from_date=None, to_date=None):
#         """
#         Process clients data with additional structure and validation.
#         Only stores clients created within the specified date range if provided.

#         Args:
#             clients_xml: XML content of clients
#             from_date: Filter clients created on or after this date (YYYY-MM-DD)
#             to_date: Filter clients created on or before this date (YYYY-MM-DD)

#         Returns:
#             Dict with processed data and storage path
#         """
#         try:
#             root = ET.fromstring(clients_xml)
#             clients_list = []
#             filtered_clients = []

#             for client in root.findall('.//client'):
#                 client_data = {}

#                 # Extract all fields with their types
#                 for field in client:
#                     field_type = field.get('type')
#                     field_value = field.text

#                     # Convert values based on type
#                     if field_type == 'integer':
#                         client_data[field.tag] = int(field_value) if field_value else None
#                     elif field_type == 'float':
#                         client_data[field.tag] = float(field_value) if field_value else None
#                     elif field_type == 'datetime':
#                         client_data[field.tag] = field_value  # Keep datetime as string
#                     else:
#                         client_data[field.tag] = field_value

#                 # Group related fields
#                 if client_data:
#                     structured_data = {
#                         'basic_info': {
#                             'id': client_data.get('id'),
#                             'client_number': client_data.get('client_number'),
#                             'created': client_data.get('created')
#                         },
#                         'personal_info': {
#                             'salutation': client_data.get('salutation'),
#                             'first_name': client_data.get('first_name'),
#                             'last_name': client_data.get('last_name'),
#                             'name': client_data.get('name')
#                         },
#                         'contact_info': {
#                             'street': client_data.get('street'),
#                             'zip': client_data.get('zip'),
#                             'city': client_data.get('city'),
#                             'country_code': client_data.get('country_code'),
#                             'phone': client_data.get('phone'),
#                             'email': client_data.get('email')
#                         },
#                         'financial_info': {
#                             'currency_code': client_data.get('currency_code'),
#                             'tax_number': client_data.get('tax_number'),
#                             'vat_number': client_data.get('vat_number')
#                         }
#                     }
#                     clients_list.append(structured_data)

#             # Filter clients by creation date if date range is provided
#             if from_date or to_date:
#                 logger.info(f"Filtering clients by date range: {from_date} to {to_date}")

#                 # Convert string dates to datetime objects for comparison
#                 from_date_obj = None
#                 to_date_obj = None

#                 if from_date:
#                     if isinstance(from_date, str):
#                         from_date_obj = datetime.fromisoformat(from_date.replace('Z', '+00:00'))
#                     else:
#                         from_date_obj = from_date

#                 if to_date:
#                     if isinstance(to_date, str):
#                         to_date_obj = datetime.fromisoformat(to_date.replace('Z', '+00:00'))
#                     else:
#                         to_date_obj = to_date

#                     # If to_date doesn't have time component, set it to end of day
#                     if to_date_obj.hour == 0 and to_date_obj.minute == 0 and to_date_obj.second == 0:
#                         to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)

#                 # Filter clients by created date
#                 for client in clients_list:
#                     created_date = None
#                     if 'basic_info' in client and 'created' in client['basic_info'] and client['basic_info']['created']:
#                         created_date = datetime.fromisoformat(client['basic_info']['created'].replace('Z', '+00:00'))

#                         # Check if client is within date range
#                         include_client = True
#                         if from_date_obj and created_date < from_date_obj:
#                             include_client = False
#                         if to_date_obj and created_date > to_date_obj:
#                             include_client = False

#                         if include_client:
#                             filtered_clients.append(client)

#                 logger.info(f"Filtered {len(filtered_clients)} clients out of {len(clients_list)}")
#             else:
#                 # No filtering, use all clients
#                 filtered_clients = clients_list

#             # Create output with metadata
#             current_date = datetime.now()
#             output_data = {
#                 'metadata': {
#                     'timestamp': current_date.isoformat(),
#                     'total_clients': len(filtered_clients),
#                     'data_format': 'JSON',
#                     'from_date': from_date.isoformat() if from_date and hasattr(from_date, 'isoformat') else from_date,
#                     'to_date': to_date.isoformat() if to_date and hasattr(to_date, 'isoformat') else to_date
#                 },
#                 'clients': filtered_clients
#             }

#             filename = f"clients_{current_date.strftime('%Y_%m_%d')}.json"
#             path = f"cbtouristenkarten-billomat/datasets/clients_customers/{filename}"

#             return {
#                 'data': output_data,
#                 'path': path
#             }
#         except Exception as e:
#             logger.error(f"Error processing clients: {str(e)}")
#             raise


#     @staticmethod
#     def xml_to_dict(xml_content):
#         """Convert XML content to dictionary."""
#         try:
#             root = ET.fromstring(xml_content)
#             result = {}
#             for child in root:
#                 if child.text:
#                     result[child.tag] = child.text
#                 else:
#                     result[child.tag] =BillomatDataProcessor._process_element(child)
#             return result
#         except ET.ParseError as e:
#             logger.error(f"Error parsing XML: {str(e)}")
#             raise

#     @staticmethod
#     def _process_element(element):
#         """Process nested XML elements."""
#         if len(element) == 0:
#             return element.text
#         result = {}
#         for child in element:
#             if child.tag not in result:
#                 result[child.tag] = child.text
#             else:
#                 if not isinstance(result[child.tag], list):
#                     result[child.tag] = [result[child.tag]]
#                 result[child.tag].append(child.text)
#         return result

#     @staticmethod
#     def process_credit_note(credit_note_xml):
#         """
#         Process credit note data and prepare it for storage.

#         Args:
#             credit_note_xml: XML content of the credit note

#         Returns:
#             dict: Processed credit note data with metadata and paths
#         """
#         try:
#             metadata =BillomatDataProcessor.xml_to_dict(credit_note_xml)

#             # Parse credit note date for folder structure
#             credit_note_date = datetime.strptime(metadata['date'], '%Y-%m-%d')
#             year = credit_note_date.strftime('%Y')
#             month = credit_note_date.strftime('%m')

#             # Parse created timestamp for filename
#             created_date = datetime.strptime(metadata['created'], '%Y-%m-%dT%H:%M:%S%z')
#             timestamp = created_date.strftime('%Y%m%d_%H%M%S')

#             credit_note_id = metadata['id']
#             client_id = metadata.get('client_id', 'unknown')
#             credit_note_number = metadata.get('credit_note_number', 'unknown')

#             # Paths for different types of data
#             paths = {
#                 'dataset': f"cbtouristenkarten-billomat/datasets/credit-notes/{year}/{month}/credit_note_{client_id}_{credit_note_id}_{timestamp}_{credit_note_number}.json"
#             }

#             return {
#                 'metadata': metadata,
#                 'paths': paths,
#                 'year': year,
#                 'month': month,
#                 'id': credit_note_id,
#                 'client_id': client_id
#             }
#         except Exception as e:
#             logger.error(f"Error processing credit note: {str(e)}")
#             raise


"""
Processes and transforms the raw data
Key functions:

    Converts XML responses to dictionaries
    Structures data for storage
    Creates folder paths based on dates
    Processes invoice metadata
    Handles file naming conventions
    Prepares data in the correct format for storage
"""

import xml.etree.ElementTree as ET
import json
import csv
from datetime import datetime, timezone, timedelta
import logging
from io import StringIO

from src.data_pipelines.common.base_processor import BaseDataProcessor
from src.infrastructure.monitoring.logging import setup_logger

logger = setup_logger(__name__)

# logger = logging.getLogger(__name__)

class BillomatDataProcessor(BaseDataProcessor):
    def process_data(self, data, data_type: str = None, **kwargs):
        """
        Implementation of the abstract process_data method from BaseDataProcessor.

        Args:
            data: The data to process (XML content)
            data_type: Type of data ('invoice', 'credit_note', 'article', 'client')
            **kwargs: Additional arguments specific to each data type

        Returns:
            dict: Processed data with appropriate structure
        """
        try:
            if data_type == 'invoice':
                pdf_metadata = kwargs.get('pdf_metadata')
                return self.process_invoice(data, pdf_metadata)
            elif data_type == 'credit_note':
                return self.process_credit_note(data)
            elif data_type == 'article':
                return self.process_articles(data,
                                          from_date=kwargs.get('from_date'),
                                          to_date=kwargs.get('to_date'))
            elif data_type == 'client':
                # Date filtering is now optional for clients
                from_date = kwargs.get('from_date')
                to_date = kwargs.get('to_date')
                if from_date or to_date:
                    return self.process_clients(data, from_date=from_date, to_date=to_date)
                else:
                    return self.process_clients(data)
            else:
                raise ValueError(f"Unsupported data type: {data_type}")

        except Exception as e:
            logger.error(f"Error processing {data_type} data: {str(e)}")
            raise

    @staticmethod
    def process_invoice(invoice_xml, pdf_metadata = None):
        """Process invoice data and prepare it for storage."""
        # Existing implementation remains the same
        metadata = BillomatDataProcessor.xml_to_dict(invoice_xml)

        # Parse invoice date for folder structure
        invoice_date = datetime.strptime(metadata['date'], '%Y-%m-%d')
        year = invoice_date.strftime('%Y')
        month = invoice_date.strftime('%m')

        # Parse created timestamp for filename
        created_date = datetime.strptime(metadata['created'], '%Y-%m-%dT%H:%M:%S%z')
        timestamp = created_date.strftime('%Y%m%d_%H%M%S')

        invoice_id = metadata['id']
        invoice_number = metadata['invoice_number']

        if pdf_metadata:
            metadata['pdf_metadata'] = pdf_metadata

        paths = {
            'dataset': f"cbtouristenkarten-billomat/datasets/invoices/{year}/{month}/invoice_{invoice_id}_{timestamp}_{invoice_number}.json",
            'pdf': f"cbtouristenkarten-billomat/invoices/{year}/{month}/invoice_{invoice_number}.pdf",
            'pdf_metadata': f"cbtouristenkarten-billomat/invoices/{year}/{month}/invoice_{invoice_number}_metadata.json"
        }

        return {
            'metadata': metadata,
            'paths': paths,
            'year': year,
            'month': month,
            'id': invoice_id
        }

    @staticmethod
    def process_articles(articles_xml, from_date=None, to_date=None):
        """
        Process articles data and convert to JSON with labels.
        Only stores articles created within the specified date range if provided.

        Args:
            articles_xml: XML content of articles
            from_date: Filter articles created on or after this date (YYYY-MM-DD)
            to_date: Filter articles created on or before this date (YYYY-MM-DD)

        Returns:
            Dict with processed data and storage path
        """
        try:
            root = ET.fromstring(articles_xml)
            articles_list = []
            filtered_articles = []

            for article in root.findall('.//article'):
                article_data = {}

                # Extract all fields with their types
                for field in article:
                    field_type = field.get('type')
                    field_value = field.text

                    # Convert values based on type
                    if field_type == 'integer':
                        article_data[field.tag] = int(field_value) if field_value else None
                    elif field_type == 'float':
                        article_data[field.tag] = float(field_value) if field_value else None
                    elif field_type == 'datetime':
                        article_data[field.tag] = field_value  # Keep datetime as string
                    else:
                        article_data[field.tag] = field_value

                articles_list.append(article_data)

            # Convert date parameters to timezone-aware datetime objects
            from_date_obj = None
            to_date_obj = None

            if from_date:
                # Normalize from_date to a timezone-aware datetime
                if isinstance(from_date, str):
                    try:
                        # Try parsing with timezone info
                        from_date_obj = datetime.fromisoformat(from_date.replace('Z', '+00:00'))
                    except ValueError:
                        # If no timezone, assume UTC
                        date_parts = from_date.split('T')[0] if 'T' in from_date else from_date
                        from_date_obj = datetime.strptime(date_parts, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                elif isinstance(from_date, datetime):
                    # Ensure datetime has timezone
                    from_date_obj = from_date if from_date.tzinfo else from_date.replace(tzinfo=timezone.utc)

                logger.info(f"Normalized from_date: {from_date_obj.isoformat()}")

            if to_date:
                # Normalize to_date to a timezone-aware datetime
                if isinstance(to_date, str):
                    try:
                        # Try parsing with timezone info
                        to_date_obj = datetime.fromisoformat(to_date.replace('Z', '+00:00'))
                    except ValueError:
                        # If no timezone, assume UTC
                        date_parts = to_date.split('T')[0] if 'T' in to_date else to_date
                        to_date_obj = datetime.strptime(date_parts, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                elif isinstance(to_date, datetime):
                    # Ensure datetime has timezone
                    to_date_obj = to_date if to_date.tzinfo else to_date.replace(tzinfo=timezone.utc)

                # If to_date doesn't have time component, set it to end of day
                if to_date_obj.hour == 0 and to_date_obj.minute == 0 and to_date_obj.second == 0:
                    to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)

                logger.info(f"Normalized to_date: {to_date_obj.isoformat()}")

            # Filter articles by creation date
            if from_date_obj or to_date_obj:
                logger.info(f"Filtering articles by date range: {from_date_obj} to {to_date_obj}")

                for article in articles_list:
                    if 'created' in article and article['created']:
                        # Convert article created date to timezone-aware datetime
                        try:
                            # Try parsing with timezone info
                            created_date = datetime.fromisoformat(article['created'].replace('Z', '+00:00'))
                        except ValueError:
                            # If no timezone info, assume UTC
                            created_date = datetime.strptime(article['created'], '%Y-%m-%dT%H:%M:%S').replace(tzinfo=timezone.utc)

                        # Check if article is within date range
                        include_article = True
                        if from_date_obj and created_date < from_date_obj:
                            include_article = False
                        if to_date_obj and created_date > to_date_obj:
                            include_article = False

                        if include_article:
                            filtered_articles.append(article)

                logger.info(f"Filtered {len(filtered_articles)} articles out of {len(articles_list)}")
            else:
                # No filtering, use all articles
                filtered_articles = articles_list
                logger.info(f"No date filtering applied. Using all {len(articles_list)} articles")

            # Create structured output with metadata
            current_date = datetime.now()
            output_data = {
                'metadata': {
                    'timestamp': current_date.isoformat(),
                    'total_articles': len(filtered_articles),  # Ensure this key exists
                    'data_format': 'JSON',
                    'from_date': from_date_obj.isoformat() if from_date_obj else None,
                    'to_date': to_date_obj.isoformat() if to_date_obj else None
                },
                'articles': filtered_articles
            }

            filename = f"articles_{current_date.strftime('%Y_%m_%d')}.json"
            path = f"cbtouristenkarten-billomat/datasets/articles/{filename}"

            # Make sure the total_articles key is in the data dict
            processed_data = {
                'data': output_data,
                'path': path
            }

            # Log the article count for verification
            logger.info(f"Total articles in processed data: {len(filtered_articles)}")

            return processed_data
        except Exception as e:
            logger.error(f"Error processing articles: {str(e)}")
            raise

    @staticmethod
    def process_clients(clients_xml, from_date=None, to_date=None):
        """
        Process clients data with additional structure and validation.
        Only stores clients created within the specified date range if provided.

        Args:
            clients_xml: XML content of clients
            from_date: Filter clients created on or after this date (YYYY-MM-DD)
            to_date: Filter clients created on or before this date (YYYY-MM-DD)

        Returns:
            Dict with processed data and storage path
        """
        try:
            root = ET.fromstring(clients_xml)
            clients_list = []
            filtered_clients = []

            for client in root.findall('.//client'):
                client_data = {}

                # Extract all fields with their types
                for field in client:
                    field_type = field.get('type')
                    field_value = field.text

                    # Convert values based on type
                    if field_type == 'integer':
                        client_data[field.tag] = int(field_value) if field_value else None
                    elif field_type == 'float':
                        client_data[field.tag] = float(field_value) if field_value else None
                    elif field_type == 'datetime':
                        client_data[field.tag] = field_value  # Keep datetime as string
                    else:
                        client_data[field.tag] = field_value

                # Group related fields
                if client_data:
                    structured_data = {
                        'basic_info': {
                            'id': client_data.get('id'),
                            'client_number': client_data.get('client_number'),
                            'created': client_data.get('created')
                        },
                        'personal_info': {
                            'salutation': client_data.get('salutation'),
                            'first_name': client_data.get('first_name'),
                            'last_name': client_data.get('last_name'),
                            'name': client_data.get('name')
                        },
                        'contact_info': {
                            'street': client_data.get('street'),
                            'zip': client_data.get('zip'),
                            'city': client_data.get('city'),
                            'country_code': client_data.get('country_code'),
                            'phone': client_data.get('phone'),
                            'email': client_data.get('email')
                        },
                        'financial_info': {
                            'currency_code': client_data.get('currency_code'),
                            'tax_number': client_data.get('tax_number'),
                            'vat_number': client_data.get('vat_number')
                        }
                    }
                    clients_list.append(structured_data)

            # Convert date parameters to timezone-aware datetime objects
            from_date_obj = None
            to_date_obj = None

            if from_date:
                # Normalize from_date to a timezone-aware datetime
                if isinstance(from_date, str):
                    try:
                        # Try parsing with timezone info
                        from_date_obj = datetime.fromisoformat(from_date.replace('Z', '+00:00'))
                    except ValueError:
                        # If no timezone, assume UTC
                        date_parts = from_date.split('T')[0] if 'T' in from_date else from_date
                        from_date_obj = datetime.strptime(date_parts, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                elif isinstance(from_date, datetime):
                    # Ensure datetime has timezone
                    from_date_obj = from_date if from_date.tzinfo else from_date.replace(tzinfo=timezone.utc)

                logger.info(f"Normalized from_date: {from_date_obj.isoformat()}")

            if to_date:
                # Normalize to_date to a timezone-aware datetime
                if isinstance(to_date, str):
                    try:
                        # Try parsing with timezone info
                        to_date_obj = datetime.fromisoformat(to_date.replace('Z', '+00:00'))
                    except ValueError:
                        # If no timezone, assume UTC
                        date_parts = to_date.split('T')[0] if 'T' in to_date else to_date
                        to_date_obj = datetime.strptime(date_parts, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                elif isinstance(to_date, datetime):
                    # Ensure datetime has timezone
                    to_date_obj = to_date if to_date.tzinfo else to_date.replace(tzinfo=timezone.utc)

                # If to_date doesn't have time component, set it to end of day
                if to_date_obj.hour == 0 and to_date_obj.minute == 0 and to_date_obj.second == 0:
                    to_date_obj = to_date_obj.replace(hour=23, minute=59, second=59)

                logger.info(f"Normalized to_date: {to_date_obj.isoformat()}")

            # Filter clients by creation date
            if from_date_obj or to_date_obj:
                logger.info(f"Filtering clients by date range: {from_date_obj} to {to_date_obj}")

                for client in clients_list:
                    created_date = None
                    if 'basic_info' in client and 'created' in client['basic_info'] and client['basic_info']['created']:
                        try:
                            # Try parsing with timezone info
                            created_date = datetime.fromisoformat(client['basic_info']['created'].replace('Z', '+00:00'))
                        except (ValueError, TypeError):
                            try:
                                # If no timezone info, assume UTC
                                created_date = datetime.strptime(client['basic_info']['created'], '%Y-%m-%dT%H:%M:%S').replace(tzinfo=timezone.utc)
                            except (ValueError, TypeError):
                                # If we can't parse the date, skip this client
                                logger.warning(f"Could not parse created date for client: {client['basic_info'].get('id')}. Date: {client['basic_info'].get('created')}")
                                continue

                        # Check if client is within date range
                        include_client = True
                        if from_date_obj and created_date < from_date_obj:
                            include_client = False
                        if to_date_obj and created_date > to_date_obj:
                            include_client = False

                        if include_client:
                            filtered_clients.append(client)

                logger.info(f"Filtered {len(filtered_clients)} clients out of {len(clients_list)}")
            else:
                # No filtering, use all clients
                filtered_clients = clients_list
                logger.info(f"No date filtering applied. Using all {len(clients_list)} clients")

            # Create output with metadata
            current_date = datetime.now()
            output_data = {
                'metadata': {
                    'timestamp': current_date.isoformat(),
                    'total_clients': len(filtered_clients),  # Ensure this key exists
                    'data_format': 'JSON',
                    'from_date': from_date_obj.isoformat() if from_date_obj else None,
                    'to_date': to_date_obj.isoformat() if to_date_obj else None
                },
                'clients': filtered_clients
            }

            filename = f"clients_{current_date.strftime('%Y_%m_%d')}.json"
            path = f"cbtouristenkarten-billomat/datasets/clients_customers/{filename}"

            # Make sure the total_clients key is in the data dict
            processed_data = {
                'data': output_data,
                'path': path
            }

            # Log the client count for verification
            logger.info(f"Total clients in processed data: {len(filtered_clients)}")

            return processed_data
        except Exception as e:
            logger.error(f"Error processing clients: {str(e)}")
            raise


    @staticmethod
    def xml_to_dict(xml_content):
        """Convert XML content to dictionary."""
        try:
            root = ET.fromstring(xml_content)
            result = {}
            for child in root:
                if child.text:
                    result[child.tag] = child.text
                else:
                    result[child.tag] =BillomatDataProcessor._process_element(child)
            return result
        except ET.ParseError as e:
            logger.error(f"Error parsing XML: {str(e)}")
            raise

    @staticmethod
    def _process_element(element):
        """Process nested XML elements."""
        if len(element) == 0:
            return element.text
        result = {}
        for child in element:
            if child.tag not in result:
                result[child.tag] = child.text
            else:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child.text)
        return result

    @staticmethod
    def process_credit_note(credit_note_xml, client_data=None):
        """
        Process credit note data and prepare it for storage.

        Args:
            credit_note_xml: XML content of the credit note
            client_data: Optional client data to include with the credit note

        Returns:
            dict: Processed credit note data with metadata and paths
        """
        try:
            metadata = BillomatDataProcessor.xml_to_dict(credit_note_xml)

            # Parse credit note date for folder structure
            credit_note_date = datetime.strptime(metadata['date'], '%Y-%m-%d')
            year = credit_note_date.strftime('%Y')
            month = credit_note_date.strftime('%m')

            # Parse created timestamp for filename
            created_date = datetime.strptime(metadata['created'], '%Y-%m-%dT%H:%M:%S%z')
            timestamp = created_date.strftime('%Y%m%d_%H%M%S')

            credit_note_id = metadata['id']
            client_id = metadata.get('client_id', 'unknown')
            credit_note_number = metadata.get('credit_note_number', 'unknown')

            # Add customer information if available
            customer_name = 'unknown_customer'
            try:
                if client_data:
                    client_info = BillomatDataProcessor.xml_to_dict(client_data)

                    # Only proceed if client_info is valid
                    if client_info and isinstance(client_info, dict):
                        # Add customer info to metadata
                        metadata['customer_info'] = {
                            'name': client_info.get('name', ''),
                            'first_name': client_info.get('first_name', ''),
                            'last_name': client_info.get('last_name', ''),
                            'salutation': client_info.get('salutation', ''),
                            'email': client_info.get('email', ''),
                            'phone': client_info.get('phone', '')
                        }

                        # Extract customer name for the filename
                        name_value = client_info.get('name', '')
                        if name_value and isinstance(name_value, str):
                            # Replace spaces with underscores
                            name_value = name_value.replace(' ', '_')
                            # Remove any problematic characters
                            name_value = ''.join(c for c in name_value if c.isalnum() or c == '_')
                            # Limit length for filename
                            if name_value:
                                customer_name = name_value[:30]
            except Exception as e:
                logger.warning(f"Error processing client data for credit note: {str(e)}")
                # Continue with default customer_name

            # Paths for different types of data - include customer name in the path
            paths = {
                'dataset': f"cbtouristenkarten-billomat/datasets/credit-notes/{year}/{month}/credit_note_{client_id}_{credit_note_id}_{timestamp}_{credit_note_number}_{customer_name}.json"
            }

            return {
                'metadata': metadata,
                'paths': paths,
                'year': year,
                'month': month,
                'id': credit_note_id,
                'client_id': client_id
            }
        except Exception as e:
            logger.error(f"Error processing credit note: {str(e)}")
            raise