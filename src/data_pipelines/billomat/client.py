"""
Handles all communication with the Billomat API
Key features:
        Makes authenticated requests to Billomat's endpoints
        Retrieves invoices, clients, and articles data
        Fetches PDF versions of invoices
        Includes retry logic for failed requests
        Extracts metadata from PDFs (like dates, customer info)
        Has robust error handling for API calls
"""

import requests
import xml.etree.ElementTree as ET
from typing import Optional, Dict, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from src.data_pipelines.common.base_client import BaseAPIClient
from src.infrastructure.monitoring.logging import setup_logger

from datetime import datetime
import logging
import time
import base64
import re
import json
import io
import PyPDF2


logger = setup_logger(__name__) #new code


class BillomatClient(BaseAPIClient):

    def __init__(self, api_key: str):
        # Get the base URL first
        base_url = "https://cbtouristenkarten.billomat.net/api"
        # Call parent's init with required arguments
        super().__init__(base_url=base_url, api_key=api_key)

    def _get_headers(self) -> Dict[str, str]:
        """Implementation of abstract method to get headers."""
        return {
            'X-BillomatApiKey': self.api_key,
            'Content-Type': 'application/xml'
        }

    def _get_base_url(self) -> str:
        """Implementation of abstract method to get base URL."""
        return self.base_url

    def get_invoice(self, invoice_id):
        """Retrieve a single invoice by ID."""
        try:
            url = f"/invoices/{invoice_id}"
            # response = requests.get(url, headers=self.headers)
            # response.raise_for_status()
            response = self._make_request(url) # code for the multiple session
            return response.content
        except requests.exceptions.RequestException as e:
            logger.error(f"Error retrieving invoice {invoice_id}: {str(e)}")
            raise


    def get_invoices(self, from_date=None, to_date=None, last_sync=None):
        """
        Retrieve invoices with optional filtering.

        Args:
            from_date: Filter invoices from this date
            to_date: Filter invoices until this date
            last_sync: Only get invoices modified after this timestamp

        Returns:
            XML content with invoice data
        """
        try:
            url = f"/invoices"
            params = {}

            # Use different filtering strategies based on what's available
            if last_sync:
                # If we have a last sync time, use it for filtering
                logger.info(f"Retrieving invoices modified since {last_sync}")
                # Convert ISO format to Billomat expected format if needed
                try:
                    sync_dt = datetime.fromisoformat(last_sync)
                    formatted_date = sync_dt.strftime('%Y-%m-%d')
                    params['from'] = formatted_date
                except ValueError:
                    logger.warning(f"Could not parse last_sync timestamp: {last_sync}")
            elif from_date:
                # Fall back to date range filtering
                params['from'] = from_date
                if to_date:
                    params['to'] = to_date

            response = self._make_request(url, params=params)
            return response.content
        except requests.exceptions.RequestException as e:
            logger.error(f"Error retrieving invoices: {str(e)}")
            raise



    def get_invoice_pdf(self, invoice_id):
        """
        Retrieve PDF version of an invoice with multiple retrieval strategies.

        Args:
            invoice_id (str): ID of the invoice

        Returns:
            dict: Contains base64 encoded PDF and extracted metadata
        """
        try:
            # Strategy 1: Direct PDF endpoint
            pdf_data = self._get_pdf_direct(invoice_id)
            if pdf_data:
                return pdf_data

            # Strategy 2: Alternative PDF retrieval method
            pdf_data = self._get_pdf_alternative(invoice_id)
            if pdf_data:
                return pdf_data

            raise ValueError(f"Could not retrieve PDF for invoice {invoice_id}")

        except Exception as e:
            logger.error(f"Error retrieving invoice PDF {invoice_id}: {str(e)}")
            raise

    def _get_pdf_direct(self, invoice_id):
        """
        Try to retrieve PDF using direct endpoint with optional parameters.

        Args:
            invoice_id (str): ID of the invoice

        Returns:
            dict or None: PDF data with metadata or None if retrieval fails
        """
        try:
            # Try different endpoint variations
            endpoints = [
                f"/invoices/{invoice_id}/pdf",
                f"/invoices/{invoice_id}/pdf?format=pdf",
                f"/invoices/{invoice_id}/pdf?type=print"
            ]

            for url in endpoints:
                try:
                    # response = requests.get(url, headers=self.headers)
                    # response.raise_for_status()
                    response = self._make_request(url)

                    # Check if response is XML (might contain base64 content)
                    if 'application/xml' in response.headers.get('Content-Type', ''):
                        root = ET.fromstring(response.text)
                        base64_content = root.find('.//base64file')
                        if base64_content is not None:
                            base64_pdf = base64_content.text
                            return self._process_base64_pdf(base64_pdf)

                    # If it's directly a PDF
                    if 'application/pdf' in response.headers.get('Content-Type', ''):
                        base64_pdf = base64.b64encode(response.content).decode('utf-8')
                        return self._process_base64_pdf(base64_pdf)

                except requests.exceptions.HTTPError as http_err:
                    # Log but continue to next endpoint
                    logger.warning(f"HTTP error for {url}: {http_err}")
                    continue

            return None

        except Exception as e:
            logger.error(f"Error in _get_pdf_direct: {str(e)}")
            return None

    def _get_pdf_alternative(self, invoice_id):
        """
        Alternative method to retrieve PDF using invoice details first.

        Args:
            invoice_id (str): ID of the invoice

        Returns:
            dict or None: PDF data with metadata or None if retrieval fails
        """
        try:
            # First, get invoice details to confirm existence
            invoice_url = f"/invoices/{invoice_id}"
            # response = requests.get(invoice_url, headers=self.headers)
            # response.raise_for_status()
            response  = self._make_request(invoice_url)

            # Then try PDF retrieval
            pdf_url = f"/invoices/{invoice_id}/pdf"
            # pdf_response = requests.get(pdf_url, headers=self.headers)
            # pdf_response.raise_for_status()
            pdf_response = self._make_request(pdf_url)

            # Assuming response is base64 or direct PDF
            base64_pdf = base64.b64encode(pdf_response.content).decode('utf-8')
            return self._process_base64_pdf(base64_pdf)

        except Exception as e:
            logger.error(f"Error in _get_pdf_alternative: {str(e)}")
            return None

    def _process_base64_pdf(self, base64_pdf):
        """
        Process base64 PDF to extract metadata.

        Args:
            base64_pdf (str): Base64 encoded PDF content

        Returns:
            dict: PDF data with metadata
        """
        try:
            # Decode base64 PDF
            pdf_bytes = base64.b64decode(base64_pdf)
            pdf_file = io.BytesIO(pdf_bytes)
            pdf_reader = PyPDF2.PdfReader(pdf_file)

            # Extract text from first page
            text = pdf_reader.pages[0].extract_text() if pdf_reader.pages else ""

            # Extract metadata
            metadata = {
                'pdf_base64': base64_pdf,
                'metadata': {
                    'offer_number': self._extract_offer_number(text),
                    'dates': self._extract_dates(text),
                    'Dataset' : self._extract_all_fields(text)
                }
            }

            return metadata

        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            return None

    def _extract_offer_number(self, text):
        """
        Extract offer number using regex.

        Args:
            text (str): Text to search for offer number

        Returns:
            str or None: Extracted offer number
        """
        pattern = re.compile(r'Reiseangebotsnummer\s*([0-9]{4}-[A-Z]+-[0-9A-Z]+-[0-9])')
        match = pattern.search(text)
        return match.group(1) if match else None


    def _extract_dates(self, text):
        """
        Extract various dates from PDF text with support for multiple formats and languages.

        Args:
            text (str): Text to search for dates

        Returns:
            dict: Extracted dates with standardized format
        """
        def parse_date_string(date_str):
            """Helper function to parse date string in various formats"""
            try:
                if '/' in date_str:
                    try:
                        # Try MM/DD/YYYY first for English format
                        return datetime.strptime(date_str, '%m/%d/%Y')
                    except ValueError:
                        # Try DD/MM/YYYY
                        return datetime.strptime(date_str, '%d/%m/%Y')
                elif '.' in date_str:
                    return datetime.strptime(date_str, '%d.%m.%Y')
                elif '-' in date_str:
                    return datetime.strptime(date_str, '%d-%m-%Y')
            except ValueError:
                return None
            return None

        # Define date patterns for different formats
        date_format_patterns = [
            r'(\d{2}\.\d{2}\.\d{4})',  # DD.MM.YYYY
            r'(\d{2}/\d{2}/\d{4})',    # DD/MM/YYYY or MM/DD/YYYY
            r'(\d{2}-\d{2}-\d{4})'     # DD-MM-YYYY
        ]

        # Combined date patterns with labels
        date_patterns = {
            'invoice_date': [
                (r'Rechnungsdatum\s*{}', 'German'),
                (r'Invoice Date\s*{}', 'English')
            ],
            'travel_date': [
                (r'Reisedatum\s*{}', 'German'),
                (r'Travel Date\s*{}', 'English')
            ],
            'booking_date': [
                (r'Buchungsdatum\s*{}', 'German'),
                (r'Booking Date\s*{}', 'English')
            ],
            'arrival_date': [
                (r'Anreisedatum\s*{}', 'German'),
                (r'Ankunftsdatum\s*{}', 'German'),
                (r'Date of Arrival\s*{}', 'English'),
                (r'Arrival Date\s*{}', 'English')
            ]
        }

        dates = {}
        # Try each label and format combination
        for label, label_patterns in date_patterns.items():
            for base_pattern, language in label_patterns:
                for date_format in date_format_patterns:
                    pattern = re.compile(base_pattern.format(date_format))
                    match = pattern.search(text)
                    if match:
                        date_str = match.group(1)
                        date_obj = parse_date_string(date_str)
                        if date_obj:
                            dates[label] = date_obj.strftime('%Y-%m-%d')
                            break  # Break after finding first valid date for this label
                if label in dates:
                    break  # Break if we've found a date for this label

        return dates


    def _extract_all_fields(self, text):
        """
        Extract all relevant fields from PDF text including personal information,
        reference numbers, and dates.

        Args:
            text (str): Text content from PDF

        Returns:
            dict: Dictionary containing all extracted fields
        """
        def clean_value(value):
            """Clean extracted values by removing extra whitespace"""
            if value:
                return ' '.join(value.strip().split())
            return None

        # Define patterns for all fields
        patterns = {
            # Personal Information
            'title': r'(Frau|Herr|Sig\.ra|Mr\.|Mrs\.|Ms\.)\s',
            'name': r'(?:Frau|Herr|Sig\.ra|Mr\.|Mrs\.|Ms\.)\s+([^\n]+?)(?:\s+(?:·|\n|\d))',
            'street': r'(?:straße|str\.|via|street)\s*(\d+[^\n]*)',
            'address_line1': r'([^\n]+(?:straße|str\.|via|street)[^\n]+)',
            'postal_code': r'(\b\d{5}\b|\bCP\s+\d{5}\b)',
            'city': r'\d{5}\s+([^\n]+)',
            'country': r'(Deutschland|Italia|Germany|Italy)',

            # Reference Numbers
            'customer_number': r'(?:Kundennummer|Client Number)\s*(TS-KD\d+)',
            'invoice_number': r'(?:Rechnungsnummer|Invoice Number)\s*(TS-RE\d+)',
            'booking_reference': r'(?:Reiseangebotsnummer|Booking Reference)\s*([\w-]+)',

            # Dates - using various formats and labels
            'invoice_date': r'(?:Rechnungsdatum|Invoice Date)\s*(\d{2}\.\d{2}\.\d{4})',
            'due_date': r'(?:Fälligkeitsdatum|Invoice Due Date)\s*(\d{2}\.\d{2}\.\d{4})',
            'arrival_date': r'(?:Anreisedatum|Date of Arrival)\s*(\d{2}\.\d{2}\.\d{4})',

            # Financial Information
            'total_amount': r'Gesamtbetrag EUR\s*([\d.,]+)',
            'payment_reference': r'Verwendungszweck\s*"([^"]+)"'
        }

        # Extract all fields
        extracted_data = {}

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                # Get the first capturing group
                value = match.group(1) if match.groups() else match.group(0)
                extracted_data[field] = clean_value(value)

        # Process address components into a structured format
        address_components = {
            'street': extracted_data.get('street'),
            'postal_code': extracted_data.get('postal_code'),
            'city': extracted_data.get('city'),
            'country': extracted_data.get('country')
        }

        # Add formatted full address
        full_address = ' '.join(filter(None, address_components.values()))
        if full_address:
            extracted_data['full_address'] = full_address

        # Convert dates to standardized format (YYYY-MM-DD)
        date_fields = ['invoice_date', 'due_date', 'arrival_date']
        for date_field in date_fields:
            if date_field in extracted_data:
                try:
                    date_obj = datetime.strptime(extracted_data[date_field], '%d.%m.%Y')
                    extracted_data[date_field] = date_obj.strftime('%Y-%m-%d')
                except ValueError:
                    pass

        return extracted_data


    # def get_clients(self):
    #     """Retrieve all clients."""
    #     try:
    #         url = f"/clients"
    #         response = requests.get(url, headers=self.headers)
    #         response.raise_for_status()
    #         return response.content
    #     except requests.exceptions.RequestException as e:
    #         logger.error(f"Error retrieving clients: {str(e)}")
    #         raise

    def get_article(self, article_id):
        """Retrieve a single article by ID."""
        try:
            url = f"/articles/{article_id}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.content
        except requests.exceptions.RequestException as e:
            logger.error(f"Error retrieving article {article_id}: {str(e)}")
            raise

    def get_articles(self):
        """Retrieve all articles with pagination handling."""
        try:
            all_articles = []
            page = 1
            per_page = 100

            while True:
                url = f"/articles"
                params = {'page': page, 'per_page': per_page}
                # response = requests.get(url, headers=self.headers, params=params)
                # response.raise_for_status()
                response = self._make_request(url,params=params)

                # Parse XML response
                root = ET.fromstring(response.content)
                articles = root.findall('.//article')

                if not articles:
                    break

                all_articles.extend(articles)
                page += 1

            # Convert back to XML string
            result_root = ET.Element('articles')
            for article in all_articles:
                result_root.append(article)

            return ET.tostring(result_root, encoding='UTF-8')
        except requests.exceptions.RequestException as e:
            logger.error(f"Error retrieving articles: {str(e)}")
            raise

    def get_client(self, client_id):
        """
        Retrieve a single client by ID.

        Args:
            client_id: The ID of the client to retrieve

        Returns:
            XML content with client data or None if client not found
        """
        try:
            url = f"/clients/{client_id}"
            logger.info(f"Retrieving client with ID: {client_id}")

            try:
                response = self._make_request(url)

                # Verify that we got valid client data
                root = ET.fromstring(response.content)
                client_element = root.find('.//client') or root.tag == 'client'

                if client_element:
                    logger.info(f"Successfully retrieved client with ID: {client_id}")
                    return response.content
                else:
                    logger.warning(f"Response doesn't contain client data for ID: {client_id}")
                    return None

            except ET.ParseError:
                logger.warning(f"Could not parse response as XML for client ID: {client_id}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Error retrieving client {client_id}: {str(e)}")
            # Return None instead of raising to allow processing to continue
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving client {client_id}: {str(e)}")
            return None

    def get_clients(self, get_last_pages=True, num_pages=3, per_page=100):
        """
        Retrieve clients with pagination handling.

        Args:
            get_last_pages: If True, retrieves the last pages instead of starting from page 1
            num_pages: Number of pages to retrieve
            per_page: Number of clients per page (default: 100)

        Returns:
            XML string containing client data
        """
        try:
            all_clients = []
            base_delay = 1  # Start with 1 second delay
            # Note: We might use this for exponential backoff in the future
            # max_delay = 60

            # First, determine the total number of pages if we need the last pages
            if get_last_pages:
                # Make a request to get the total count
                url = f"/clients"
                params = {'page': 1, 'per_page': 1}  # Just get one record to check total
                logger.info(f"Determining total number of client pages...")
                response = self._make_request(url, params=params)

                # Parse XML response to get total count
                root = ET.fromstring(response.content)

                # According to the Billomat API docs, the pagination info is in the root element attributes
                # Example: <clients type="array" page="1" per_page="100" total="2">
                total_attr = root.get('total')
                per_page_attr = root.get('per_page')

                if total_attr and per_page_attr:
                    # We have the pagination info in the attributes
                    total_clients = int(total_attr)
                    per_page_actual = int(per_page_attr)

                    # Calculate total pages, ensuring at least 1 page
                    total_pages = max(1, (total_clients + per_page_actual - 1) // per_page_actual)  # Ceiling division

                    # Calculate starting page to get the last num_pages
                    start_page = max(1, total_pages - num_pages + 1)
                    logger.info(f"Total clients from attributes: {total_clients}, Total pages: {total_pages}, Starting from page: {start_page}")
                else:
                    # Try to find total element in the XML
                    total_element = root.find('.//total')
                    if total_element is not None and total_element.text:
                        total_clients = int(total_element.text)
                        # Calculate total pages, ensuring at least 1 page
                        total_pages = max(1, (total_clients + per_page - 1) // per_page)  # Ceiling division

                        # Calculate starting page to get the last num_pages
                        start_page = max(1, total_pages - num_pages + 1)
                        logger.info(f"Total clients from element: {total_clients}, Total pages: {total_pages}, Starting from page: {start_page}")
                    else:
                        logger.warning("Could not determine total client count, starting from page 1")
                        start_page = 1
            else:
                # Start from page 1 if not getting last pages
                start_page = 1

            # Now retrieve the pages we want
            page = start_page
            pages_retrieved = 0

            # If the starting page is very high (like 41589), it's likely an error
            # Let's add a safety check
            if start_page > 1000:  # Arbitrary high number that's unlikely in real data
                logger.warning(f"Starting page {start_page} seems too high, resetting to a reasonable value")
                start_page = max(1, 10 - num_pages)  # Start from page that will give us the last few pages

            consecutive_empty_pages = 0
            max_empty_pages = 3  # Stop after this many consecutive empty pages

            while pages_retrieved < num_pages:
                try:
                    url = f"/clients"
                    params = {'page': page, 'per_page': per_page}
                    # Add delay before making request
                    logger.info(f"Fetching page {page} of clients. Waiting {base_delay} seconds...")
                    time.sleep(base_delay)
                    response = self._make_request(url, params=params)
                    logger.info(f"Retrieved page {page} of clients")

                    # Parse XML response
                    root = ET.fromstring(response.content)
                    clients = root.findall('.//client')

                    if not clients:
                        logger.info(f"No clients found on page {page}")
                        consecutive_empty_pages += 1

                        # If we've seen too many empty pages in a row, assume we're past the end
                        if consecutive_empty_pages >= max_empty_pages:
                            logger.info(f"Reached {max_empty_pages} consecutive empty pages, stopping pagination")
                            break

                        # Try the next page anyway
                        page += 1
                        continue

                    # Reset empty page counter when we find clients
                    consecutive_empty_pages = 0

                    # Add clients to our collection
                    all_clients.extend(clients)
                    logger.info(f"Added {len(clients)} clients from page {page}")

                    page += 1
                    pages_retrieved += 1

                    if pages_retrieved >= num_pages:
                        logger.info(f"Retrieved {pages_retrieved} pages as requested")
                        break

                except Exception as e:
                    logger.warning(f"Error retrieving page {page}: {str(e)}")
                    # Try to continue with the next page
                    page += 1
                    consecutive_empty_pages += 1

                    if consecutive_empty_pages >= max_empty_pages:
                        logger.warning(f"Too many errors or empty pages, stopping pagination")
                        break

            # Convert back to XML string
            result_root = ET.Element('clients')
            for client in all_clients:
                result_root.append(client)

            logger.info(f"Total clients retrieved: {len(all_clients)}")
            return ET.tostring(result_root, encoding='UTF-8')
        except requests.exceptions.RequestException as e:
            logger.error(f"Error retrieving clients: {str(e)}")
            raise

#New changes starts here

    def get_credit_notes(self, from_date=None, to_date=None, status=None):
        """
        Retrieve all credit notes within the date range.

        Args:
            from_date: Filter credit notes from this date (YYYY-MM-DD)
            to_date: Filter credit notes to this date (YYYY-MM-DD)
            status: Filter by status (DRAFT, OPEN, PAID) or comma-separated list

        Returns:
            XML content with credit notes data
        """
        try:
            url = f"/credit-notes"
            params = {}

            # Add filters if provided
            if from_date:
                params['from'] = from_date
            if to_date:
                params['to'] = to_date
            if status:
                params['status'] = status

            # Make the request with parameters
            logger.info(f"Retrieving credit notes with params: {params}")
            response = self._make_request(url, params=params)

            # Log pagination info if available
            try:
                root = ET.fromstring(response.content)
                total_attr = root.get('total')
                page_attr = root.get('page')
                per_page_attr = root.get('per_page')

                if total_attr and page_attr and per_page_attr:
                    logger.info(f"Retrieved credit notes: page {page_attr} of {(int(total_attr) + int(per_page_attr) - 1) // int(per_page_attr)}, total records: {total_attr}")
            except Exception as parse_error:
                logger.warning(f"Could not parse pagination info: {str(parse_error)}")

            return response.content
        except Exception as e:
            logger.error(f"Error retrieving credit notes: {str(e)}")
            raise

    def get_credit_note(self, credit_note_id):
        """Retrieve a single credit note by ID."""
        try:
            url = f"/credit-notes/{credit_note_id}"
            response = self._make_request(url)
            return response.content
        except Exception as e:
            logger.error(f"Error retrieving credit note {credit_note_id}: {str(e)}")
            raise