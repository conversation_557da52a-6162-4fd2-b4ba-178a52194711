from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict

@dataclass
class InvoiceMetadata:
    id: str
    date: datetime
    invoice_number: str
    client_id: str
    total_amount: float
    status: str
    created: datetime
    updated: Optional[datetime] = None

@dataclass
class ClientMetadata:
    id: str
    client_number: str
    name: str
    created: datetime
    email: Optional[str] = None
    phone: Optional[str] = None
    country_code: Optional[str] = None

@dataclass
class ArticleMetadata:
    id: str
    title: str
    unit_price: float
    created: datetime
    description: Optional[str] = None
    tax_rate: Optional[float] = None

@dataclass
class CreditNoteMetadata:
    id: str
    credit_note_number: str
    client_id: str
    date: datetime
    total_amount: float
    status: str
    created: datetime
    invoice_id: Optional[str] = None