# Billomat Data Integration with Google Cloud

This project implements an automated data pipeline that retrieves data from Billomat API, processes it, and stores it in Google Cloud Storage. It uses Google Cloud Scheduler and Cloud Functions to automate the data retrieval and processing.

## Project Overview

The system retrieves the following data types from Billomat:
- Invoices (including PDF attachments)
- Credit Notes (with customer information)
- Articles
- Clients (retrieving the most recent data)

The data is stored in Google Cloud Storage with appropriate organization and structure. The system also tracks invoice payment status and updates the data accordingly.

This project is production-ready with a complete GitLab CI/CD pipeline for automated testing and deployment.

## Features

- **Automated Data Retrieval**: Scheduled retrieval of data from Billomat API
- **Comprehensive Data Processing**: Handles all major Billomat data types
- **PDF Handling**: Retrieves and stores invoice PDFs
- **Status Tracking**: Monitors invoice payment status changes
- **Month-End Processing**: Special processing at the end of each month to check for status changes
- **Robust Error Handling**: Continues processing even when parts fail
- **Detailed Logging**: Comprehensive logging for monitoring and debugging
- **CI/CD Pipeline**: Automated testing and deployment with GitLab CI/CD
- **Production-Ready**: Complete with testing, deployment, and monitoring

## Project Structure

```
google_cloud_storage/
├── src/
│   ├── cloud_functions/
│   │   └── ingestions/
│   │       └── billomat/
│   │           ├── main.py                # Cloud Function entry point
│   │           └── requirements.txt       # Cloud Function dependencies
│   ├── data_pipelines/
│   │   └── billomat/
│   │       ├── client.py                  # Billomat API client
│   │       └── processor.py               # Data processing logic
│   └── infrastructure/
│       └── gcs/
│           └── storage_manager/
│               └── billomat_storage.py    # GCS storage operations
├── terraform/
│   ├── main.tf                            # Terraform configuration
│   ├── variables.tf                       # Terraform variables
│   └── prepare_deployment.sh              # Deployment script
├── tests/
│   ├── e2e/
│   │   └── test_billomat_e2e.py           # End-to-end tests
│   └── README.md                          # Testing documentation
├── ci/
│   └── authenticate_gcp.sh                # GCP authentication script for CI/CD
├── .gitlab-ci.yml                         # GitLab CI/CD configuration
├── run_tests.sh                           # Script to run tests
└── README.md                              # This file
```

## Prerequisites

- Google Cloud Platform account with billing enabled
- Billomat API credentials
- Python 3.7 or higher
- Terraform
- Google Cloud SDK
- GitLab account (for CI/CD)

## Local Development Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd google_cloud_storage
```

### 2. Set Up Environment Variables

Create a `.env` file in the root directory with the following variables:

```
BILLOMAT_API_KEY=your_billomat_api_key
BILLOMAT_API_URL=your_billomat_api_url
GCP_PROJECT_ID=your_gcp_project_id
GCS_BUCKET_NAME=your_gcs_bucket_name
GCP_REGION=your_gcp_region
```

### 3. Install Dependencies

```bash
pip install --upgrade google-cloud-storage
pip install -r requirements.txt
```

### 4. Configure Google Cloud SDK

```bash
gcloud init
gcloud auth application-default login
gcloud config set project your_gcp_project_id
```

## Local Deployment

The project uses Terraform to deploy the infrastructure to Google Cloud Platform.

### 1. Navigate to the Terraform Directory

```bash
cd terraform
```

### 2. Initialize Terraform

```bash
terraform init
```

### 3. Plan Terraform Deployment

```bash
terraform plan -var="billomat_api_key=your_api_key"
```

### 4. Prepare Deployment Package

```bash
bash prepare_deployment.sh
```

### 5. Apply Terraform Configuration

```bash
terraform apply
```

This will deploy:
- Google Cloud Storage bucket
- Google Cloud Functions
- Google Cloud Scheduler jobs

## GitLab CI/CD Pipeline

This project includes a GitLab CI/CD pipeline that automates testing, building, and deployment.

### Pipeline Stages

1. **Prepare**: Fixes code issues and prepares the environment
2. **Test**: Runs unit tests and linting
3. **Build**: Prepares the deployment package and runs Terraform plan
4. **Deploy**: Deploys the infrastructure to Google Cloud Platform (manual approval required)
5. **Cleanup**: Runs after deployment to clean up temporary files (only if deployment was executed)

### Setting Up GitLab CI/CD

1. **Create a Service Account**: Create a GCP service account with the necessary permissions
2. **Generate a Key**: Generate a JSON key for the service account
3. **Add CI/CD Variables**: In GitLab, go to Settings > CI/CD > Variables and add the following variables:
   - `GCP_SERVICE_ACCOUNT_KEY`: The content of the service account JSON key
   - `GCP_PROJECT_ID`: Your Google Cloud project ID
   - `GCP_REGION`: Your Google Cloud region
   - `GCS_BUCKET_NAME`: Your GCS bucket name
   - `BILLOMAT_API_KEY`: Your Billomat API key
   - `GOOGLE_APPLICATION_CREDENTIALS`: Set to `${CI_PROJECT_DIR}/gcp-service-account.json`

### Running the Pipeline

The pipeline will run automatically when you push to the main branch or create a merge request.

To manually trigger the pipeline:
1. Go to CI/CD > Pipelines
2. Click "Run Pipeline"
3. Select the branch and click "Run Pipeline"

### Detailed CI/CD Documentation

For detailed information about the CI/CD pipeline, see [docs/gitlab_ci_cd_guide.md](docs/gitlab_ci_cd_guide.md) and [docs/python_version_compatibility.md](docs/python_version_compatibility.md).

## Usage

Once deployed, the system will automatically:

1. Retrieve data from Billomat according to the schedule
2. Process and store the data in Google Cloud Storage
3. Track invoice status changes
4. Perform month-end processing

### Manual Triggering

You can manually trigger the Cloud Function using the Google Cloud Console or the gcloud CLI:

```bash
gcloud functions call billomat-ingestion --data '{"data_types": ["invoices", "credit_notes", "clients", "articles"], "from_date": "2023-01-01", "to_date": "2023-01-31"}'
```

Yopu can also use the curl command to call the url API of cloud run function of the scheduler
``` bash
curl -X POST {billomat_ingestion_url}
```

### Viewing the Data

You can view the stored data in Google Cloud Storage using the Google Cloud Console or the gsutil CLI:

```bash
gsutil ls gs://your_gcs_bucket_name/cbtouristenkarten-billomat/
```

## Data Organization

The data is organized in Google Cloud Storage as follows:

```
cbtouristenkarten-billomat/
├── datasets/
│   ├── invoices/
│   │   └── YYYY/
│   │       └── MM/
│   │           └── invoice_CLIENT-ID_INVOICE-ID_TIMESTAMP_INVOICE-NUMBER.json
│   ├── credit-notes/
│   │   └── YYYY/
│   │       └── MM/
│   │           └── credit_note_CLIENT-ID_CREDIT-NOTE-ID_TIMESTAMP_CREDIT-NOTE-NUMBER_CUSTOMER-NAME.json
│   ├── clients_customers/
│   │   └── clients_YYYY_MM_DD.json
│   └── articles/
│       └── articles_YYYY_MM_DD.json
├── invoices/
│   └── YYYY/
│       └── MM/
│           ├── invoice_INVOICE-NUMBER.pdf
│           └── invoice_INVOICE-NUMBER_metadata.json
└── metadata/
    ├── paid_invoices.json
    └── unpaid_invoices.json
```

## Testing

The project includes comprehensive tests to validate all functionality.

### Running Tests

```bash
./run_tests.sh
```

For more details on testing, see [tests/README.md](tests/README.md).

## Monitoring

You can monitor the execution of the Cloud Functions using Google Cloud Logging:

```bash
gcloud logging read "resource.type=cloud_function AND resource.labels.function_name=billomat-ingestion"
```

## Troubleshooting

### Common Issues

1. **API Rate Limiting**: If you encounter rate limiting from the Billomat API, adjust the delay parameters in the client.

2. **Missing Data**: Check the logs for any errors during data retrieval or processing.

3. **Deployment Failures**: Ensure that your GCP service account has the necessary permissions.

### Logs

Check the Cloud Function logs for detailed error messages:

```bash
gcloud functions logs read billomat-ingestion
```


## Deployment Commands Summary

### Local Deployment
```bash
cd terraform
terraform init
terraform plan -var="billomat_api_key=your_api_key"
bash prepare_deployment.sh
terraform apply
```

### GitLab CI/CD Deployment
1. Push changes to the main branch or create a merge request
2. GitLab CI/CD pipeline will run automatically
3. Approve the deployment in the GitLab CI/CD interface by clicking the "Play" button on the deploy_to_gcp job

### Troubleshooting CI/CD Pipeline

If you encounter issues with the CI/CD pipeline:

1. **Terraform Execution Errors**: The pipeline uses Alpine Linux with Terraform installed manually to ensure shell scripts can be executed properly.

2. **Authentication Issues**: Make sure your GCP_SERVICE_ACCOUNT_KEY variable is correctly set in GitLab CI/CD variables.

3. **Deployment Script**: The pipeline uses ci/deploy_to_gcp.sh to handle the deployment process. Check this script if you encounter deployment issues.


## References

- [Billomat API Documentation](https://www.billomat.com/en/api/)
- [Google Cloud Platform Documentation](https://cloud.google.com/docs)
- [Terraform Documentation](https://www.terraform.io/docs)